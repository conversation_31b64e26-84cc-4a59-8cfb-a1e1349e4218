#!/usr/bin/env python3
"""
Test script to verify the frontend can access the fund details API and get allocation data
"""

import requests
import json

def test_fund_details_api():
    print("🧪 Testing Fund Details API Response")
    print("=" * 50)
    
    # Test the API endpoint directly
    api_url = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds/fund-58b952b6/details"
    
    try:
        print(f"📡 Calling API: {api_url}")
        response = requests.get(api_url)
        
        if response.status_code == 200:
            print("✅ API call successful")
            data = response.json()
            
            # Check if we have analytics data
            if 'data' in data and 'analytics' in data['data']:
                analytics = data['data']['analytics']
                print(f"📊 Analytics data found: {analytics.keys()}")
                
                # Check asset allocation
                if 'assetAllocation' in analytics:
                    asset_allocation = analytics['assetAllocation']
                    print(f"💰 Asset Allocation: {asset_allocation}")
                    
                    # Check if values are reasonable (not all zeros)
                    total = sum(asset_allocation.values())
                    if total > 0:
                        print(f"✅ Asset allocation has data (total: {total}%)")
                    else:
                        print("❌ Asset allocation is all zeros")
                else:
                    print("❌ No assetAllocation in analytics")
                
                # Check geographic allocation
                if 'geographicAllocation' in analytics:
                    geo_allocation = analytics['geographicAllocation']
                    print(f"🌍 Geographic Allocation: {geo_allocation}")
                    
                    total = sum(geo_allocation.values())
                    if total > 0:
                        print(f"✅ Geographic allocation has data (total: {total}%)")
                    else:
                        print("❌ Geographic allocation is all zeros")
                else:
                    print("❌ No geographicAllocation in analytics")
                
                # Check sector allocation
                if 'sectorAllocation' in analytics:
                    sector_allocation = analytics['sectorAllocation']
                    print(f"🏢 Sector Allocation: {len(sector_allocation)} sectors")
                    
                    if len(sector_allocation) > 0:
                        print("✅ Sector allocation has data")
                        for sector in sector_allocation[:3]:  # Show first 3
                            print(f"   - {sector.get('name', 'Unknown')}: {sector.get('percentage', 0)}%")
                    else:
                        print("❌ Sector allocation is empty")
                else:
                    print("❌ No sectorAllocation in analytics")
                
                # Check top holdings
                if 'topHoldings' in analytics:
                    top_holdings = analytics['topHoldings']
                    print(f"📈 Top Holdings: {len(top_holdings)} holdings")
                    
                    if len(top_holdings) > 0:
                        print("✅ Top holdings has data")
                        for holding in top_holdings[:3]:  # Show first 3
                            print(f"   - {holding.get('name', 'Unknown')}: {holding.get('percentage', 0)}%")
                    else:
                        print("❌ Top holdings is empty")
                else:
                    print("❌ No topHoldings in analytics")
                    
            else:
                print("❌ No analytics data in response")
                print(f"Response keys: {data.keys() if 'data' in data else 'No data key'}")
        else:
            print(f"❌ API call failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error calling API: {e}")

if __name__ == "__main__":
    test_fund_details_api()
