#!/usr/bin/env python3
"""
Test API Response Structure

This script tests if the API is returning the fund data in the correct structure
that the frontend expects, particularly for analytics and market data.
"""

import requests
import json
from typing import Dict, Any

def test_funds_api():
    """Test the funds API endpoint directly"""
    
    api_base = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    
    print("🔍 Testing Funds API...")
    print(f"API Base URL: {api_base}")
    
    # Test 1: List all funds
    print("\n1. Testing GET /funds")
    try:
        response = requests.get(f"{api_base}/funds", timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response structure: {list(data.keys())}")
            
            if 'data' in data and 'funds' in data['data']:
                funds = data['data']['funds']
                print(f"Number of funds: {len(funds)}")
                
                if funds:
                    fund = funds[0]
                    print(f"\nSample fund structure:")
                    print(f"  - Fund ID: {fund.get('fund_id', 'N/A')}")
                    print(f"  - Name: {fund.get('name', 'N/A')}")
                    print(f"  - Has analytics: {'analytics' in fund}")
                    print(f"  - Has market_data: {'market_data' in fund}")
                    print(f"  - Has holdings: {'holdings' in fund}")
                    
                    if 'analytics' in fund:
                        analytics = fund['analytics']
                        print(f"  - Analytics keys: {list(analytics.keys())}")
                        
                    if 'market_data' in fund:
                        market_data = fund['market_data']
                        print(f"  - Market data keys: {list(market_data.keys())}")
                        
                        if 'price_data' in market_data:
                            print(f"  - Price data available: ✓")
                        if 'risk_analytics' in market_data:
                            print(f"  - Risk analytics available: ✓")
                        if 'valuation_metrics' in market_data:
                            print(f"  - Valuation metrics available: ✓")
                        if 'technical_indicators' in market_data:
                            print(f"  - Technical indicators available: ✓")
                    
                    return fund['fund_id'] if 'fund_id' in fund else fund.get('id')
            else:
                print("❌ Unexpected response structure")
                return None
        else:
            print(f"❌ Request failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return None

def test_fund_details(fund_id: str):
    """Test getting detailed fund information"""
    
    api_base = "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
    
    print(f"\n2. Testing GET /funds/{fund_id}")
    try:
        response = requests.get(f"{api_base}/funds/{fund_id}", timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            fund = response.json()
            print(f"Fund details structure: {list(fund.keys())}")
            
            # Check key fields that frontend expects
            fields_to_check = [
                'fund_id', 'id', 'name', 'nav', 'fund_type',
                'analytics', 'market_data', 'holdings', 
                'performance_metrics', 'risk_analytics'
            ]
            
            print(f"\nField availability:")
            for field in fields_to_check:
                available = field in fund
                print(f"  - {field}: {'✓' if available else '✗'}")
            
            # Check analytics structure
            if 'analytics' in fund:
                analytics = fund['analytics']
                expected_analytics = ['kpis', 'riskMetrics', 'topHoldings', 'sectorAllocation', 'geographicAllocation', 'assetAllocation']
                print(f"\nAnalytics structure:")
                for key in expected_analytics:
                    available = key in analytics
                    print(f"  - {key}: {'✓' if available else '✗'}")
            
            return True
        else:
            print(f"❌ Request failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
        return False

def test_frontend_api_structure():
    """Test what the frontend conversion function expects"""
    
    print(f"\n3. Testing Frontend API Structure Expectations")
    
    # Based on the frontend API code, these are the key fields it looks for:
    expected_backend_fields = {
        'fund_id': 'Fund identifier',
        'name': 'Fund name',
        'nav': 'Net Asset Value',
        'fund_type': 'Fund type (equity, bond, etc.)',
        'expense_ratio': 'Expense ratio',
        'total_assets': 'Assets under management',
        'performance_metrics': 'Performance data',
        'risk_analytics': 'Risk analytics data',
        'holdings': 'Holdings data with top_holdings',
        'analytics': 'Frontend-formatted analytics',
        'market_data': 'Market data with price_data, risk_analytics, etc.'
    }
    
    print("Expected backend fields for frontend compatibility:")
    for field, description in expected_backend_fields.items():
        print(f"  - {field}: {description}")

def main():
    print("=" * 60)
    print("FundFlow API Response Structure Test")
    print("=" * 60)
    
    # Test the funds API
    fund_id = test_funds_api()
    
    if fund_id:
        # Test individual fund details
        success = test_fund_details(fund_id)
        
        if success:
            print(f"\n✅ API tests completed successfully!")
            print(f"✅ Fund data structure appears to be correct for frontend consumption")
        else:
            print(f"\n❌ Fund details test failed")
    else:
        print(f"\n❌ Funds API test failed")
    
    # Show what frontend expects
    test_frontend_api_structure()
    
    print("\n" + "=" * 60)
    print("Test Summary:")
    print("- The API should return funds with 'analytics' and 'market_data' fields")
    print("- Analytics should contain: kpis, riskMetrics, topHoldings, allocations")
    print("- Market data should contain: price_data, risk_analytics, technical_indicators")
    print("- If these are present, the frontend should display the data correctly")
    print("=" * 60)

if __name__ == "__main__":
    main()