#!/usr/bin/env python3
"""
Simple test script to validate historical data functionality.
"""

import os
import sys
from datetime import datetime, timezone, date
from decimal import Decimal

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Conda environment setup
if 'CONDA_DEFAULT_ENV' not in os.environ:
    print("Please activate the ff_env conda environment first:")
    print("conda activate ff_env")
    sys.exit(1)

from src.shared.database import get_fund_repository
from src.shared.repositories.historical_price_repository import get_historical_price_repository
from src.shared.models.historical_price import HistoricalPriceCreate, DataSource


def test_historical_data_functionality():
    """Test the historical data functionality."""
    print("🧪 Testing historical data functionality...")
    
    try:
        # Get repositories
        fund_repo = get_fund_repository()
        price_repo = get_historical_price_repository()
        
        # Get first fund for testing
        funds_response = fund_repo.list_funds(limit=1)
        funds = funds_response.get('funds', []) if isinstance(funds_response, dict) else funds_response
        if not funds:
            print("❌ No funds found. Please create a fund first.")
            return False
        
        test_fund = funds[0]
        print(f"📊 Testing with fund: {test_fund.fund_id} ({test_fund.name})")
        
        # Test 1: Create a sample historical price
        today = date.today()
        test_price = HistoricalPriceCreate(
            fund_id=test_fund.fund_id,
            date=today.strftime("%Y-%m-%d"),
            nav=Decimal("100.50"),
            price=Decimal("100.50"),
            volume=Decimal("50000"),
            total_return=Decimal("1.25"),
            benchmark_value=Decimal("98.75"),
            benchmark_return=Decimal("1.10"),
            data_source=DataSource.MANUAL,
            currency="USD"
        )
        
        print("✏️  Creating test historical price...")
        try:
            created_price = price_repo.create(test_price)
            print(f"✅ Created price: {created_price.date} - NAV: ${created_price.nav}")
        except ValueError as e:
            if "already exists" in str(e):
                print(f"ℹ️  Price already exists for today, continuing test...")
            else:
                raise e
        
        # Test 2: Retrieve the price
        print("📖 Retrieving historical price...")
        retrieved_price = price_repo.get_by_fund_and_date(
            test_fund.fund_id, 
            today.strftime("%Y-%m-%d")
        )
        
        if retrieved_price:
            print(f"✅ Retrieved price: {retrieved_price.date} - NAV: ${retrieved_price.nav}")
        else:
            print("❌ Failed to retrieve price")
            return False
        
        # Test 3: Get price history
        print("📈 Getting price history...")
        price_history = price_repo.get_fund_price_history(test_fund.fund_id, limit=10)
        print(f"✅ Retrieved {len(price_history)} historical prices")
        
        if price_history:
            print("📊 Recent prices:")
            for price in price_history[:3]:
                print(f"  {price.date}: NAV=${price.nav}, Return={price.total_return}%")
        
        # Test 4: Test price range summary
        print("📊 Testing price range summary...")
        
        from datetime import timedelta
        start_date = (date.today() - timedelta(days=30)).strftime("%Y-%m-%d")
        end_date = date.today().strftime("%Y-%m-%d")
        
        summary = price_repo.get_price_range_summary(test_fund.fund_id, start_date, end_date)
        
        print(f"✅ Price range summary generated: {summary['count']} data points")
        print(f"📈 NAV range: ${summary['min_nav']} - ${summary['max_nav']}")
        
        if summary['total_return'] is not None:
            print(f"📈 Total return: {summary['total_return']:.2f}%")
        
        print("\n🎉 All tests passed! Historical data functionality is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 Starting historical data validation tests...\n")
    
    # Check environment
    env = os.environ.get('ENVIRONMENT', 'dev')
    print(f"Environment: {env}")
    
    success = test_historical_data_functionality()
    
    if success:
        print("\n✅ All tests completed successfully!")
        print("\n📝 Next steps:")
        print("1. Deploy the updated stack: sam build && sam deploy --config-env dev")
        print("2. Run population script: python populate_historical_data.py")
        print("3. Test the /funds/{id}/historical API endpoint")
    else:
        print("\n❌ Tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()