#!/usr/bin/env python3
"""
Script to populate historical price data for existing funds.
This script generates realistic historical price data for all funds in the database.
"""

import os
import sys
import random
from datetime import datetime, timezone, timedelta, date
from decimal import Decimal
from typing import List, Dict, Any

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Conda environment setup
if 'CONDA_DEFAULT_ENV' not in os.environ:
    print("Please activate the ff_env conda environment first:")
    print("conda activate ff_env")
    sys.exit(1)

import boto3
from src.shared.database import get_fund_repository
from src.shared.repositories.historical_price_repository import get_historical_price_repository
from src.shared.models.historical_price import HistoricalPriceCreate, DataSource


def generate_historical_prices_for_fund(fund_id: str, fund_nav: Decimal, fund_type: str, start_date: date, end_date: date) -> List[HistoricalPriceCreate]:
    """Generate realistic historical price data for a fund."""
    prices = []
    current_date = start_date
    current_nav = fund_nav
    
    # Set volatility based on fund type
    volatility_map = {
        "equity": 0.02,      # 2% daily volatility
        "bond": 0.005,       # 0.5% daily volatility
        "mixed": 0.015,      # 1.5% daily volatility
        "money_market": 0.001, # 0.1% daily volatility
        "alternative": 0.025,  # 2.5% daily volatility
        "index": 0.018,      # 1.8% daily volatility
        "etf": 0.018,        # 1.8% daily volatility
    }
    
    daily_volatility = volatility_map.get(fund_type.lower(), 0.015)
    
    # Generate trend (slight upward bias for funds)
    annual_return = random.uniform(0.03, 0.12)  # 3% to 12% annual return
    daily_trend = (1 + annual_return) ** (1/365) - 1
    
    # Generate benchmark starting value (usually close to fund NAV)
    benchmark_nav = current_nav * Decimal(str(random.uniform(0.95, 1.05)))
    current_benchmark = benchmark_nav
    benchmark_volatility = daily_volatility * 0.8  # Benchmark typically less volatile
    
    while current_date <= end_date:
        # Skip weekends (funds typically don't trade on weekends)
        if current_date.weekday() < 5:  # Monday = 0, Friday = 4
            
            # Generate daily return
            random_return = random.gauss(daily_trend, daily_volatility)
            current_nav = current_nav * Decimal(str(1 + random_return))
            
            # Generate benchmark return
            benchmark_return = random.gauss(daily_trend * 0.9, benchmark_volatility)
            current_benchmark = current_benchmark * Decimal(str(1 + benchmark_return))
            
            # Generate volume (random but realistic)
            volume = Decimal(str(random.uniform(10000, 1000000)))
            
            # Create historical price record
            price_data = HistoricalPriceCreate(
                fund_id=fund_id,
                date=current_date.strftime("%Y-%m-%d"),
                nav=current_nav.quantize(Decimal('0.01')),  # Round to 2 decimal places
                price=current_nav.quantize(Decimal('0.01')),  # For funds, price = NAV
                volume=volume.quantize(Decimal('1')),
                total_return=Decimal(str(random_return * 100)).quantize(Decimal('0.01')),  # Percentage
                benchmark_value=current_benchmark.quantize(Decimal('0.01')),
                benchmark_return=Decimal(str(benchmark_return * 100)).quantize(Decimal('0.01')),
                data_source=DataSource.MANUAL,
                currency="USD"
            )
            
            prices.append(price_data)
        
        current_date += timedelta(days=1)
    
    return prices


def main():
    """Main function to populate historical data."""
    print("Starting historical data population...")
    
    # Check environment
    env = os.environ.get('ENVIRONMENT', 'dev')
    print(f"Environment: {env}")
    
    try:
        # Get repositories
        fund_repo = get_fund_repository()
        price_repo = get_historical_price_repository()
        
        # Get all funds
        print("Fetching all funds...")
        funds_response = fund_repo.list_funds(limit=1000)  # Get up to 1000 funds
        funds = funds_response.get('funds', []) if isinstance(funds_response, dict) else funds_response
        
        if not funds:
            print("No funds found in database. Please create some funds first.")
            return
        
        print(f"Found {len(funds)} funds to populate with historical data")
        
        # Date range for historical data (1 year back)
        end_date = datetime.now(timezone.utc).date()
        start_date = end_date - timedelta(days=365)
        
        print(f"Generating historical data from {start_date} to {end_date}")
        
        total_prices_created = 0
        
        for i, fund in enumerate(funds, 1):
            print(f"Processing fund {i}/{len(funds)}: {fund.fund_id} ({fund.name})")
            
            # Skip if fund doesn't have NAV
            if not fund.nav or fund.nav <= 0:
                print(f"  Skipping fund {fund.fund_id} - no valid NAV")
                continue
            
            # Check if historical data already exists
            existing_data = price_repo.get_fund_price_history(
                fund.fund_id,
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            if existing_data:
                print(f"  Fund {fund.fund_id} already has {len(existing_data)} historical price points, skipping")
                continue
            
            # Generate historical prices
            try:
                fund_type_str = fund.fund_type.value if hasattr(fund.fund_type, 'value') else str(fund.fund_type)
                historical_prices = generate_historical_prices_for_fund(
                    fund.fund_id,
                    fund.nav,
                    fund_type_str,
                    start_date,
                    end_date
                )
                
                print(f"  Generated {len(historical_prices)} price points")
                
                # Batch create historical prices
                result = price_repo.batch_create(historical_prices)
                
                success_count = result.get('success_count', 0)
                error_count = result.get('error_count', 0)
                
                if error_count > 0:
                    print(f"  Created {success_count} prices with {error_count} errors")
                    errors = result.get('errors', [])
                    for error in errors[:3]:  # Show first 3 errors
                        print(f"    Error: {error}")
                else:
                    print(f"  Successfully created {success_count} historical prices")
                
                total_prices_created += success_count
                
            except Exception as e:
                print(f"  Error processing fund {fund.fund_id}: {e}")
                continue
        
        print(f"\n✅ Historical data population completed!")
        print(f"Total historical prices created: {total_prices_created}")
        print(f"Funds processed: {len(funds)}")
        
        # Show some sample data
        if funds:
            sample_fund = funds[0]
            sample_prices = price_repo.get_fund_price_history(
                sample_fund.fund_id,
                limit=5
            )
            
            if sample_prices:
                print(f"\nSample historical data for {sample_fund.fund_id}:")
                for price in sample_prices[:3]:
                    print(f"  {price.date}: NAV=${price.nav}, Return={price.total_return}%")
        
    except Exception as e:
        print(f"❌ Error during population: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()