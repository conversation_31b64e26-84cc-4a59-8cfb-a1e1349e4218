# Environment Configuration
# Copy this file to .env.local and update the values

# Application Environment
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# For testing with external tunnels (replace with your actual tunnel URL)
# NEXT_PUBLIC_APP_URL=https://your-tunnel-url.ngrok-free.app
# NEXTAUTH_URL=https://your-tunnel-url.ngrok-free.app
# Example with pinggy.link:
# NEXT_PUBLIC_APP_URL=https://rmzqmzxtlz.a.pinggy.link
# NEXTAUTH_URL=https://rmzqmzxtlz.a.pinggy.link

# AWS API Configuration
NEXT_PUBLIC_API_BASE_URL=https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev
NEXT_PUBLIC_AWS_REGION=ap-northeast-1
NEXT_PUBLIC_USE_AWS_API=true

# Local API Configuration (fallback)
# NEXT_PUBLIC_API_URL=http://localhost:3001
API_SECRET_KEY=your-secret-key-here

# Database Configuration (if connecting directly from frontend)
# DATABASE_URL=postgresql://username:password@localhost:5432/fundflow

# Authentication - NextAuth.js with AWS Cognito
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:3000

# AWS Cognito Configuration
# IMPORTANT: These must match your AWS Cognito User Pool settings exactly
COGNITO_CLIENT_ID=your-cognito-client-id
COGNITO_CLIENT_SECRET=your-cognito-client-secret
COGNITO_ISSUER=https://cognito-idp.{region}.amazonaws.com/{userPoolId}

# Cognito Setup Checklist:
# 1. In AWS Cognito User Pool → App Integration → App clients
# 2. Add callback URL: http://localhost:3000/api/auth/callback/cognito
# 3. Add ngrok callback URL: https://your-ngrok-url.ngrok-free.app/api/auth/callback/cognito
# 4. Add sign out URL: http://localhost:3000
# 5. Add ngrok sign out URL: https://your-ngrok-url.ngrok-free.app
# 6. Enable OAuth 2.0 scopes: openid, profile, email
# 7. Enable OAuth 2.0 grant types: Authorization code grant
# 8. Set Identity providers: Cognito User Pool

# Cognito MFA Configuration (Optional - managed in AWS Console)
# When MFA is enabled in Cognito User Pool:
# - SMS: Requires phone number verification
# - TOTP: Supports authenticator apps (Google Authenticator, Authy, etc.)
# - Software Token: Alternative to SMS for second factor

# Third-party Services (examples for financial app)
# PLAID_CLIENT_ID=your-plaid-client-id
# PLAID_SECRET=your-plaid-secret
# PLAID_ENVIRONMENT=sandbox

# Analytics (optional)
# NEXT_PUBLIC_GA_ID=your-google-analytics-id
# NEXT_PUBLIC_POSTHOG_KEY=your-posthog-key

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_DEBUG=true

# Mock Mode Configuration (Consolidated)
# Set to 'true' to enable complete mock mode (login, API fallback, and data)
# Set to 'false' to use real AWS services (Cognito authentication, API Gateway, DynamoDB)
# WARNING: Only use in development - never in production
NEXT_PUBLIC_ENABLE_MOCK_MODE=false

# Legacy Mock Configuration (DEPRECATED - use NEXT_PUBLIC_ENABLE_MOCK_MODE instead)
# These flags are maintained for backward compatibility but will be removed in future versions
# NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false
# NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false

# SSL Configuration
# Path to custom CA certificate bundle (automatically set by setup script)
SSL_CA_BUNDLE_PATH=./certs/cacert.pem

# Use system CA certificates in addition to custom bundle
SSL_USE_SYSTEM_CA=true

# SSL connection timeout in milliseconds
SSL_TIMEOUT=10000

# For development only - set to '0' to disable SSL verification (NOT RECOMMENDED)
# NODE_TLS_REJECT_UNAUTHORIZED=1

# SSL Certificate Troubleshooting:
# If you encounter UNABLE_TO_GET_ISSUER_CERT_LOCALLY errors:
# 1. Run: npm run setup:ssl
# 2. Validate: npm run validate:ssl
# 3. Use dev:ssl script: npm run dev:ssl
# 4. Check corporate firewall/proxy settings
# 5. Contact IT if in corporate environment