#!/usr/bin/env node

/**
 * Test script to verify portfolio mock mode functionality
 */

const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '..', '.env.local');

console.log('🧪 Portfolio Mock Mode Test');
console.log('============================');

// Check if .env.local exists
if (!fs.existsSync(envPath)) {
  console.log('❌ .env.local file not found');
  console.log('💡 Create .env.local file with NEXT_PUBLIC_ENABLE_MOCK_MODE=true to test');
  process.exit(1);
}

// Read environment variables
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

const envVars = {};
envLines.forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

console.log('📋 Current Environment Configuration:');
console.log('-----------------------------------');

const mockMode = envVars['NEXT_PUBLIC_ENABLE_MOCK_MODE'] === 'true';
const mockFallback = envVars['NEXT_PUBLIC_ENABLE_MOCK_FALLBACK'] === 'true';
const mockLogin = envVars['NEXT_PUBLIC_ENABLE_MOCK_LOGIN'] === 'true';
const useAwsApi = envVars['NEXT_PUBLIC_USE_AWS_API'] === 'true';

console.log(`NEXT_PUBLIC_ENABLE_MOCK_MODE: ${envVars['NEXT_PUBLIC_ENABLE_MOCK_MODE'] || 'not set'}`);
console.log(`NEXT_PUBLIC_ENABLE_MOCK_FALLBACK: ${envVars['NEXT_PUBLIC_ENABLE_MOCK_FALLBACK'] || 'not set'}`);
console.log(`NEXT_PUBLIC_ENABLE_MOCK_LOGIN: ${envVars['NEXT_PUBLIC_ENABLE_MOCK_LOGIN'] || 'not set'}`);
console.log(`NEXT_PUBLIC_USE_AWS_API: ${envVars['NEXT_PUBLIC_USE_AWS_API'] || 'not set'}`);

console.log('\n🎯 Portfolio Mock Mode Analysis:');
console.log('--------------------------------');

if (mockMode) {
  console.log('✅ Consolidated Mock Mode ENABLED');
  console.log('   📊 Portfolio data: Will use mock portfolios');
  console.log('   🔐 Authentication: Mock login available');
  console.log('   🌐 API calls: Will return mock data immediately');
  console.log('   ⚡ Performance: Fast response times (no network calls)');
  console.log('   🎭 UI indicator: "Mock Mode" badge will be shown');
} else if (mockLogin && mockFallback) {
  console.log('✅ Legacy Mock Mode ENABLED');
  console.log('   📊 Portfolio data: Will use mock portfolios on API failure');
  console.log('   🔐 Authentication: Mock login available');
  console.log('   🌐 API calls: Will try real API first, fallback to mock');
} else if (mockFallback) {
  console.log('⚠️  Partial Mock Mode (Fallback Only)');
  console.log('   📊 Portfolio data: Will use mock portfolios on API failure');
  console.log('   🔐 Authentication: Real Cognito only');
  console.log('   🌐 API calls: Will try real API first, fallback to mock');
} else {
  console.log('❌ Mock Mode DISABLED');
  console.log('   📊 Portfolio data: Real AWS DynamoDB only');
  console.log('   🔐 Authentication: Real Cognito only');
  console.log('   🌐 API calls: Real AWS API Gateway only');
  console.log('   ⚠️  Requires: AWS setup and authentication');
}

console.log('\n🚀 Testing Recommendations:');
console.log('---------------------------');

if (mockMode) {
  console.log('✅ Perfect setup for portfolio testing!');
  console.log('   1. Start the dev server: npm run dev');
  console.log('   2. Navigate to: http://localhost:3000/portfolios');
  console.log('   3. Use mock login if needed');
  console.log('   4. You should see 10 mock portfolios with realistic data');
  console.log('   5. All CRUD operations will work with mock data');
} else {
  console.log('💡 To enable complete portfolio mock mode:');
  console.log('   Add this line to your .env.local:');
  console.log('   NEXT_PUBLIC_ENABLE_MOCK_MODE=true');
  console.log('');
  console.log('   Or use the toggle script:');
  console.log('   npm run toggle-mock mock');
}

console.log('\n📝 Mock Portfolio Features:');
console.log('---------------------------');
console.log('• 10 realistic mock portfolios');
console.log('• Various portfolio types (personal, retirement, etc.)');
console.log('• Realistic holdings and transactions');
console.log('• Performance metrics and analytics');
console.log('• Full CRUD operations (Create, Read, Update, Delete)');
console.log('• Filtering and sorting capabilities');
console.log('• No AWS dependencies required');

console.log('\n🔍 Next Steps:');
console.log('--------------');
if (mockMode) {
  console.log('1. Test portfolio listing and filtering');
  console.log('2. Test portfolio creation and editing');
  console.log('3. Test adding holdings and transactions');
  console.log('4. Verify all mock data displays correctly');
} else {
  console.log('1. Enable mock mode using the instructions above');
  console.log('2. Restart your development server');
  console.log('3. Test the portfolio functionality');
}
