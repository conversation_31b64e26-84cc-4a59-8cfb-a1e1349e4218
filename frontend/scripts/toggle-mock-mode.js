#!/usr/bin/env node

/**
 * <PERSON>ript to toggle between mock mode and external testing mode
 * This script modifies .env.local to switch between localhost and pinggy.link URLs
 */

const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '..', '.env.local');

function readEnvFile() {
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    process.exit(1);
  }
  return fs.readFileSync(envPath, 'utf8');
}

function writeEnvFile(content) {
  fs.writeFileSync(envPath, content, 'utf8');
}

function enableMockMode() {
  console.log('🎭 Enabling Mock Mode (localhost URLs)...');
  
  let content = readEnvFile();
  
  // Comment out pinggy.link URLs
  content = content.replace(
    /^NEXT_PUBLIC_APP_URL=https:\/\/rmzqmzxtlz\.a\.pinggy\.link$/m,
    '# NEXT_PUBLIC_APP_URL=https://rmzqmzxtlz.a.pinggy.link'
  );
  content = content.replace(
    /^NEXTAUTH_URL=https:\/\/rmzqmzxtlz\.a\.pinggy\.link$/m,
    '# NEXTAUTH_URL=https://rmzqmzxtlz.a.pinggy.link'
  );
  
  // Ensure localhost URLs are active
  if (!content.includes('NEXT_PUBLIC_APP_URL=http://localhost:3000')) {
    content = content.replace(
      /# Mock login mode: Use localhost URLs/,
      '# Mock login mode: Use localhost URLs\nNEXT_PUBLIC_APP_URL=http://localhost:3000'
    );
  }
  if (!content.includes('NEXTAUTH_URL=http://localhost:3000')) {
    content = content.replace(
      /NEXT_PUBLIC_APP_URL=http:\/\/localhost:3000/,
      'NEXT_PUBLIC_APP_URL=http://localhost:3000\nNEXTAUTH_URL=http://localhost:3000'
    );
  }
  
  // Enable consolidated mock mode
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_MODE=false$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_MODE=true'
  );

  // Also enable legacy flags for backward compatibility
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true'
  );
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true'
  );
  
  writeEnvFile(content);
  console.log('✅ Mock mode enabled');
  console.log('📝 Configuration:');
  console.log('   - NEXTAUTH_URL: http://localhost:3000');
  console.log('   - NEXT_PUBLIC_APP_URL: http://localhost:3000');
  console.log('   - ENABLE_MOCK_LOGIN: true');
  console.log('   - ENABLE_MOCK_FALLBACK: true');
  console.log('🔄 Please restart your development server: npm run dev');
}

function enableExternalMode() {
  console.log('🌐 Enabling External Mode (pinggy.link URLs)...');
  
  let content = readEnvFile();
  
  // Uncomment pinggy.link URLs
  content = content.replace(
    /^# NEXT_PUBLIC_APP_URL=https:\/\/rmzqmzxtlz\.a\.pinggy\.link$/m,
    'NEXT_PUBLIC_APP_URL=https://rmzqmzxtlz.a.pinggy.link'
  );
  content = content.replace(
    /^# NEXTAUTH_URL=https:\/\/rmzqmzxtlz\.a\.pinggy\.link$/m,
    'NEXTAUTH_URL=https://rmzqmzxtlz.a.pinggy.link'
  );
  
  // Comment out localhost URLs
  content = content.replace(
    /^NEXT_PUBLIC_APP_URL=http:\/\/localhost:3000$/m,
    '# NEXT_PUBLIC_APP_URL=http://localhost:3000'
  );
  content = content.replace(
    /^NEXTAUTH_URL=http:\/\/localhost:3000$/m,
    '# NEXTAUTH_URL=http://localhost:3000'
  );
  
  // Disable consolidated mock mode
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_MODE=true$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_MODE=false'
  );

  // Also disable legacy flags for backward compatibility
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false'
  );
  content = content.replace(
    /^NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true$/m,
    'NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false'
  );
  
  writeEnvFile(content);
  console.log('✅ External mode enabled');
  console.log('📝 Configuration:');
  console.log('   - NEXTAUTH_URL: https://rmzqmzxtlz.a.pinggy.link');
  console.log('   - NEXT_PUBLIC_APP_URL: https://rmzqmzxtlz.a.pinggy.link');
  console.log('   - ENABLE_MOCK_LOGIN: false');
  console.log('🔄 Please restart your development server: npm run dev');
}

function showStatus() {
  const content = readEnvFile();
  
  const isLocalhost = content.includes('NEXTAUTH_URL=http://localhost:3000');
  const isPinggy = content.includes('NEXTAUTH_URL=https://rmzqmzxtlz.a.pinggy.link');
  const mockModeEnabled = content.includes('NEXT_PUBLIC_ENABLE_MOCK_MODE=true');
  const mockLoginEnabled = content.includes('NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true');
  const mockFallbackEnabled = content.includes('NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true');
  
  console.log('📋 Current Configuration Status:');
  console.log('================================');
  
  if (isLocalhost) {
    console.log('✅ Mode: Mock/Localhost');
    console.log('   - NEXTAUTH_URL: http://localhost:3000');
  } else if (isPinggy) {
    console.log('🌐 Mode: External/Pinggy.link');
    console.log('   - NEXTAUTH_URL: https://rmzqmzxtlz.a.pinggy.link');
  } else {
    console.log('⚠️  Mode: Unknown/Mixed configuration');
  }
  
  console.log(`   - Mock Mode (Consolidated): ${mockModeEnabled ? 'Enabled' : 'Disabled'}`);
  console.log(`   - Mock Login (Legacy): ${mockLoginEnabled ? 'Enabled' : 'Disabled'}`);
  console.log(`   - Mock Fallback (Legacy): ${mockFallbackEnabled ? 'Enabled' : 'Disabled'}`);
}

// Parse command line arguments
const command = process.argv[2];

switch (command) {
  case 'mock':
  case 'localhost':
    enableMockMode();
    break;
  case 'external':
  case 'pinggy':
    enableExternalMode();
    break;
  case 'status':
    showStatus();
    break;
  default:
    console.log('🎭 Mock Mode Toggle Script');
    console.log('=========================');
    console.log('');
    console.log('Usage:');
    console.log('  npm run toggle-mock mock      # Enable mock mode (localhost)');
    console.log('  npm run toggle-mock external  # Enable external mode (pinggy.link)');
    console.log('  npm run toggle-mock status    # Show current status');
    console.log('');
    console.log('Aliases:');
    console.log('  mock = localhost');
    console.log('  external = pinggy');
    console.log('');
    showStatus();
    break;
}
