#!/usr/bin/env node

/**
 * Test script to verify mock login functionality
 * This script checks if the mock login environment variables are properly configured
 * and validates the authentication flow.
 */

const fs = require('fs');
const path = require('path');

console.log('🎭 Mock Login Test Script');
console.log('========================\n');

// Check if .env.local exists
const envPath = path.join(__dirname, '..', '.env.local');
const envExists = fs.existsSync(envPath);

if (!envExists) {
  console.log('❌ .env.local file not found');
  console.log('💡 Create .env.local from env.example and configure mock settings');
  process.exit(1);
}

// Read environment variables
const envContent = fs.readFileSync(envPath, 'utf8');
const envLines = envContent.split('\n');

const envVars = {};
envLines.forEach(line => {
  const [key, value] = line.split('=');
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

console.log('📋 Environment Configuration Check:');
console.log('-----------------------------------');

// Check required variables for mock login
const requiredVars = [
  'NEXT_PUBLIC_ENABLE_MOCK_MODE'  // New consolidated flag
];

// Legacy variables (for backward compatibility check)
const legacyVars = [
  'NEXT_PUBLIC_ENABLE_MOCK_FALLBACK',
  'NEXT_PUBLIC_ENABLE_MOCK_LOGIN'
];

const optionalVars = [
  'NEXT_PUBLIC_USE_AWS_API',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL'
];

let allGood = true;

// Check required variables
requiredVars.forEach(varName => {
  const value = envVars[varName];
  if (value === 'true') {
    console.log(`✅ ${varName}=${value}`);
  } else if (value === 'false') {
    console.log(`⚠️  ${varName}=${value} (mock features disabled)`);
  } else {
    console.log(`❌ ${varName}=${value || 'not set'} (should be 'true' or 'false')`);
    allGood = false;
  }
});

// Check optional variables
console.log('\n📋 Optional Configuration:');
console.log('-------------------------');
optionalVars.forEach(varName => {
  const value = envVars[varName];
  if (value) {
    console.log(`✅ ${varName}=${value}`);
  } else {
    console.log(`⚠️  ${varName}=not set`);
  }
});

// Provide recommendations
console.log('\n🎯 Mock Login Configuration Recommendations:');
console.log('--------------------------------------------');

const mockMode = envVars['NEXT_PUBLIC_ENABLE_MOCK_MODE'] === 'true';
const mockFallback = envVars['NEXT_PUBLIC_ENABLE_MOCK_FALLBACK'] === 'true';
const mockLogin = envVars['NEXT_PUBLIC_ENABLE_MOCK_LOGIN'] === 'true';
const useAwsApi = envVars['NEXT_PUBLIC_USE_AWS_API'] === 'true';

if (mockMode) {
  console.log('✅ Perfect! Consolidated mock mode enabled');
  console.log('   - Mock authentication available');
  console.log('   - Mock data fallback enabled');
  console.log('   - No AWS dependencies required');
  console.log('   - Single flag controls all mock functionality');
} else if (mockLogin && mockFallback) {
  console.log('✅ Legacy mock mode enabled (consider upgrading to NEXT_PUBLIC_ENABLE_MOCK_MODE)');
  console.log('   - Mock authentication available');
  console.log('   - Mock data fallback enabled');
  console.log('   - No AWS dependencies required');
} else if (mockLogin && !mockFallback) {
  console.log('⚠️  Mock login enabled but mock fallback disabled');
  console.log('   - Mock authentication available');
  console.log('   - API calls will fail without real backend');
  console.log('   - Recommendation: Enable NEXT_PUBLIC_ENABLE_MOCK_MODE=true');
} else if (!mockLogin && mockFallback) {
  console.log('⚠️  Mock fallback enabled but mock login disabled');
  console.log('   - Only real Cognito authentication available');
  console.log('   - Mock data used when API fails');
  console.log('   - Recommendation: Enable NEXT_PUBLIC_ENABLE_MOCK_MODE=true for full mock mode');
} else {
  console.log('❌ Mock features disabled');
  console.log('   - Only real authentication and API calls');
  console.log('   - Requires AWS Cognito and backend setup');
  console.log('   - For local development, consider enabling NEXT_PUBLIC_ENABLE_MOCK_MODE=true');
}

console.log('\n🚀 Quick Setup for Complete Mock Mode:');
console.log('-------------------------------------');
console.log('Add this line to your .env.local:');
console.log('');
console.log('NEXT_PUBLIC_ENABLE_MOCK_MODE=true  # Enables all mock functionality');
console.log('');
console.log('Or use the legacy flags (deprecated):');
console.log('NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true');
console.log('NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true');
console.log('NEXT_PUBLIC_USE_AWS_API=false  # Optional: force mock mode');
console.log('');

console.log('🧪 Testing Instructions:');
console.log('------------------------');
console.log('1. Start the development server: npm run dev');
console.log('2. Navigate to http://localhost:3000');
console.log('3. Click "Sign In"');
if (mockLogin) {
  console.log('4. Look for "Mock Login (Development Only)" button');
  console.log('5. Use any email/password to sign in');
  console.log('6. Test API calls - they should return mock data');
} else {
  console.log('4. Only Cognito authentication will be available');
  console.log('5. You\'ll need valid AWS Cognito credentials');
}

console.log('\n📚 Documentation:');
console.log('-----------------');
console.log('- Setup Guide: MOCK_LOGIN_SETUP.md');
console.log('- Test Page: http://localhost:3000/test-auth');
console.log('- Environment Example: env.example');

if (allGood && mockLogin && mockFallback) {
  console.log('\n🎉 Configuration looks good! Ready for mock development.');
  process.exit(0);
} else {
  console.log('\n⚠️  Configuration needs attention. See recommendations above.');
  process.exit(1);
}
