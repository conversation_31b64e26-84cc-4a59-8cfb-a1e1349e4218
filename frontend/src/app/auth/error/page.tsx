'use client'

import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Suspense, useEffect, useState } from 'react'

function AuthErrorContent() {
  const searchParams = useSearchParams()
  const error = searchParams.get('error')
  const [isClient, setIsClient] = useState(false)

  // Ensure we only render environment variables on the client
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Log all search params for debugging
  console.log('🔍 Auth Error Page - Search Params:', {
    error,
    allParams: Object.fromEntries(searchParams.entries())
  })

  const getErrorDetails = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return {
          title: 'Server Configuration Error',
          description: 'There is a problem with the authentication configuration. Please contact support.',
          suggestion: 'Try refreshing the page or contact our support team if the problem persists.'
        }
      case 'AccessDenied':
        return {
          title: 'Access Denied',
          description: 'You do not have permission to access this resource.',
          suggestion: 'Please make sure you have the correct permissions or contact an administrator.'
        }
      case 'Verification':
        return {
          title: 'Verification Error',
          description: 'The verification link has expired or has already been used.',
          suggestion: 'Please request a new verification link and try again.'
        }
      case 'Default':
      default:
        return {
          title: 'Authentication Error',
          description: 'An unexpected error occurred during authentication.',
          suggestion: 'Please try signing in again. If the problem continues, contact support.'
        }
    }
  }

  const errorDetails = getErrorDetails(error)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 text-red-600 dark:text-red-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-gray-100">
            {errorDetails.title}
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {errorDetails.description}
          </p>
        </div>

        <Card className="mt-8">
          <Card.Content>
            <div className="text-center space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {errorDetails.suggestion}
              </p>

              <div className="space-y-3">
                <Link href="/auth/signin">
                  <Button fullWidth variant="primary" size="lg">
                    Try Signing In Again
                  </Button>
                </Link>
                
                <Link href="/">
                  <Button fullWidth variant="outline" size="lg">
                    Go Home
                  </Button>
                </Link>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
                  <p>
                    Need help? Contact our{' '}
                    <a href="/support" className="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                      support team
                    </a>
                  </p>
                  {isClient && process.env.NODE_ENV === 'development' && (
                    <div className="mt-4 p-3 bg-gray-100 rounded text-left">
                      <p className="font-semibold mb-2">Debug Info:</p>
                      <p>Error: {error || 'Unknown'}</p>
                      <p>Mock Login: {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN}</p>
                      <p>Mock Fallback: {process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK}</p>
                      <p>NextAuth URL: {process.env.NEXTAUTH_URL}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
}

export default function AuthError() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 transition-colors">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    }>
      <AuthErrorContent />
    </Suspense>
  )
} 