import type { Metadata } from "next";
import "./globals.css";
import { ReduxProvider } from "@/store/Provider";
import Navigation from "@/components/features/Navigation";
import SessionProvider from "@/components/auth/SessionProvider";
import ThemeProvider from "@/components/providers/ThemeProvider";
import { LanguageProvider } from "@/i18n/provider";
import { initializeSSL } from "@/lib/ssl-init";
import { Lato } from "next/font/google";

// Configure Lato font
const lato = Lato({
  subsets: ['latin'],
  weight: ['300', '400', '700', '900'],
  variable: '--font-lato',
  display: 'swap',
});

// Initialize SSL configuration when the app loads
if (typeof window === 'undefined') {
  // Server-side initialization
  initializeSSL();
}

export const metadata: Metadata = {
  title: "FundFlow",
  description: "Personal Finance Management Application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${lato.variable} antialiased min-h-screen transition-colors duration-200 bg-gray-50 dark:bg-gray-900`} suppressHydrationWarning>
        <LanguageProvider>
          <SessionProvider>
            <ReduxProvider>
              <ThemeProvider>
                <Navigation />
                <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                  {children}
                </main>
              </ThemeProvider>
            </ReduxProvider>
          </SessionProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
