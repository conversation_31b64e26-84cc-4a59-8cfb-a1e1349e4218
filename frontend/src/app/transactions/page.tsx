import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { formatCurrency, formatDate } from '@/utils';

export default function TransactionsPage() {
  // Mock data - in real app, this would come from API/Redux
  const transactions = [
    { id: '1', description: 'Coffee Shop', amount: -4.50, category: 'Food & Dining', date: '2024-01-09', type: 'expense' as const },
    { id: '2', description: 'Salary Deposit', amount: 5200.00, category: 'Salary', date: '2024-01-08', type: 'income' as const },
    { id: '3', description: 'Grocery Store', amount: -89.30, category: 'Food & Dining', date: '2024-01-07', type: 'expense' as const },
    { id: '4', description: 'Gas Station', amount: -45.20, category: 'Transportation', date: '2024-01-06', type: 'expense' as const },
    { id: '5', description: 'Freelance Payment', amount: 1200.00, category: 'Freelance', date: '2024-01-05', type: 'income' as const },
    { id: '6', description: 'Restaurant', amount: -67.80, category: 'Food & Dining', date: '2024-01-04', type: 'expense' as const },
  ];

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Transactions</h1>
          <p className="text-gray-600 mt-2">Track and manage your financial transactions.</p>
        </div>
        <Button>Add Transaction</Button>
      </div>

      {/* Filters */}
      <Card>
        <Card.Content>
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <input
                type="text"
                placeholder="Search transactions..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Categories</option>
              <option value="food">Food & Dining</option>
              <option value="transportation">Transportation</option>
              <option value="salary">Salary</option>
              <option value="freelance">Freelance</option>
            </select>
            <select className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="">All Types</option>
              <option value="income">Income</option>
              <option value="expense">Expense</option>
            </select>
            <Button variant="outline">Filter</Button>
          </div>
        </Card.Content>
      </Card>

      {/* Transactions List */}
      <Card>
        <Card.Header>
          <Card.Title>All Transactions</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Description</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Amount</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody>
                {transactions.map((transaction) => (
                  <tr key={transaction.id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <td className="py-3 px-4">
                      <div className="font-medium text-gray-900">{transaction.description}</div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        {transaction.category}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-gray-500">
                      {formatDate(new Date(transaction.date))}
                    </td>
                    <td className={`py-3 px-4 text-right font-semibold ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm">Edit</Button>
                        <Button variant="ghost" size="sm">Delete</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card.Content>
        <Card.Footer>
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500">Showing 6 of 6 transactions</p>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>Previous</Button>
              <Button variant="outline" size="sm" disabled>Next</Button>
            </div>
          </div>
        </Card.Footer>
      </Card>
    </div>
  );
} 