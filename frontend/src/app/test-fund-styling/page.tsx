'use client';

import { useState } from 'react';
import { Card, Button } from '@/components/ui';
import { useTheme } from '@/hooks/useTheme';

export default function TestFundStylingPage() {
  const { mode, isDark, isInitialized, toggleTheme } = useTheme();

  if (!isInitialized) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6 sm:space-y-8">
        
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 sm:p-6 transition-colors">
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4 lg:gap-6">
            <div className="flex-1 min-w-0">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-3">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100 truncate">
                  Test Fund Name
                </h1>
                <span className="px-2 py-1 text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded self-start transition-colors">
                  TESTFUND
                </span>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">
                This is a test fund for styling verification
              </p>
            </div>
            <div className="flex gap-2">
              <Button onClick={toggleTheme} variant="outline">
                {isDark ? '☀️ Light' : '🌙 Dark'} Mode
              </Button>
            </div>
          </div>
        </header>

        {/* Performance Summary */}
        <Card>
          <Card.Header>
            <Card.Title>Current Performance</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">
                  ₹125.50
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">Current NAV</div>
                <div className="text-sm font-medium mt-1 text-green-600 dark:text-green-400">
                  +2.5%
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">
                  ₹1,250Cr
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">AUM</div>
                <div className="text-sm font-medium mt-1 text-blue-600 dark:text-blue-400">
                  Growing
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">
                  15.2%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">1Y Return</div>
                <div className="text-sm font-medium mt-1 text-green-600 dark:text-green-400">
                  Outperforming
                </div>
              </div>
              
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">
                  1.25
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">Sharpe Ratio</div>
                <div className="text-sm font-medium mt-1 text-green-600 dark:text-green-400">
                  Excellent
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>

        {/* Fund Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Fund Information */}
          <Card>
            <Card.Header>
              <Card.Title>Fund Details</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Fund Manager</div>
                    <div className="text-gray-900 dark:text-gray-100">John Doe</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Inception Date</div>
                    <div className="text-gray-900 dark:text-gray-100">January 15, 2020</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Risk Level</div>
                    <div>
                      <span className="inline-block px-2 py-1 text-xs font-medium rounded-full transition-colors bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                        MEDIUM
                      </span>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Min Investment</div>
                    <div className="text-gray-900 dark:text-gray-100">₹5,000</div>
                  </div>
                </div>
                
                <div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Description</div>
                  <div className="text-gray-900 dark:text-gray-100 text-sm leading-relaxed">
                    This is a test fund designed to showcase the styling improvements for the fund detail page.
                    It demonstrates proper dark mode support and clean visual hierarchy.
                  </div>
                </div>
              </div>
            </Card.Content>
          </Card>

          {/* Performance History */}
          <Card>
            <Card.Header>
              <Card.Title>Performance History</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="space-y-3">
                {[
                  { period: '1 Month', value: 2.5 },
                  { period: '3 Months', value: 8.2 },
                  { period: '6 Months', value: 12.1 },
                  { period: '1 Year', value: 15.2 },
                  { period: '3 Years', value: 11.8 },
                  { period: '5 Years', value: 9.5 }
                ].map(({ period, value }) => (
                  <div key={period} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-b-0">
                    <div className="text-sm text-gray-700 dark:text-gray-300">{period}</div>
                    <div className={`font-medium ${value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                      {value >= 0 ? '+' : ''}{value.toFixed(2)}%
                    </div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <Card.Header>
              <Card.Title>Key Performance Indicators</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {[
                  { label: 'Total Return', value: 45.2, format: 'percentage' },
                  { label: 'Annualized Return', value: 12.8, format: 'percentage' },
                  { label: 'Volatility', value: 18.5, format: 'percentage' },
                  { label: 'Sharpe Ratio', value: 1.25, format: 'number' }
                ].map((item) => (
                  <div key={item.label} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex justify-between items-start mb-1">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</div>
                      <div className={`text-lg font-bold ${item.format === 'percentage' && item.value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-gray-100'}`}>
                        {item.format === 'percentage' ? `${item.value >= 0 ? '+' : ''}${item.value.toFixed(2)}%` : item.value.toFixed(2)}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Performance metric</div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>

          <Card>
            <Card.Header>
              <Card.Title>Risk Metrics</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-1 gap-4">
                {[
                  { label: 'Beta', value: 0.95 },
                  { label: 'Alpha', value: 2.1 },
                  { label: 'Max Drawdown', value: -12.5 },
                  { label: 'VaR (95%)', value: -8.2 }
                ].map((item) => (
                  <div key={item.label} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex justify-between items-start mb-1">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</div>
                      <div className={`text-lg font-bold ${item.value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {item.value >= 0 ? '+' : ''}{item.value.toFixed(2)}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">Risk measure</div>
                  </div>
                ))}
              </div>
            </Card.Content>
          </Card>
        </div>

        {/* Status */}
        <Card>
          <Card.Header>
            <Card.Title>Styling Status</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
              <h3 className="font-bold text-green-800 dark:text-green-200 mb-2">✅ Fund Detail Styling Complete</h3>
              <ul className="text-green-700 dark:text-green-300 text-sm space-y-1">
                <li>• Card components with proper dark mode support</li>
                <li>• Consistent color scheme and typography</li>
                <li>• Smooth transitions between light and dark modes</li>
                <li>• Professional visual hierarchy</li>
                <li>• Responsive design maintained</li>
              </ul>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  );
}
