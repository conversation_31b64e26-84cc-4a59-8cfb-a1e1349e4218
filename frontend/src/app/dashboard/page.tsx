'use client';

import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { formatCurrency } from '@/utils';
import { SessionInfo } from '@/components/auth';
import { useTranslation } from '@/i18n/provider';

export default function DashboardPage() {
  const { t } = useTranslation();
  
  // Mock data - in real app, this would come from API/Redux
  const dashboardData = {
    totalBalance: 12845.67,
    monthlyIncome: 5200.00,
    monthlyExpenses: 3420.50,
    savings: 1779.50,
  };

  const recentTransactions = [
    { id: '1', description: 'Coffee Shop', amount: -4.50, category: 'Food & Dining', date: '2024-01-09' },
    { id: '2', description: 'Salary Deposit', amount: 5200.00, category: 'Salary', date: '2024-01-08' },
    { id: '3', description: 'Grocery Store', amount: -89.30, category: 'Food & Dining', date: '2024-01-07' },
    { id: '4', description: 'Gas Station', amount: -45.20, category: 'Transportation', date: '2024-01-06' },
  ];

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">{t('dashboard.title')}</h1>
        <p className="text-gray-600 mt-2">{t('dashboard.subtitle')}</p>
      </div>

      {/* Financial Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card hover>
          <Card.Header>
            <Card.Title>{t('dashboard.totalBalance')}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(dashboardData.totalBalance)}
            </div>
            <p className="text-sm text-gray-500 mt-1">+2.5% from last month</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>{t('dashboard.monthlyIncome')}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-blue-600">
              {formatCurrency(dashboardData.monthlyIncome)}
            </div>
            <p className="text-sm text-gray-500 mt-1">This month</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Monthly Expenses</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(dashboardData.monthlyExpenses)}
            </div>
            <p className="text-sm text-gray-500 mt-1">-5.2% from last month</p>
          </Card.Content>
        </Card>

        <Card hover>
          <Card.Header>
            <Card.Title>Savings</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="text-2xl font-bold text-purple-600">
              {formatCurrency(dashboardData.savings)}
            </div>
            <p className="text-sm text-gray-500 mt-1">This month</p>
          </Card.Content>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <Card.Header>
          <div className="flex justify-between items-center">
            <Card.Title>Recent Transactions</Card.Title>
            <Button variant="outline" size="sm">
              View All
            </Button>
          </div>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            {recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{transaction.description}</h4>
                  <p className="text-sm text-gray-500">{transaction.category} • {transaction.date}</p>
                </div>
                <div className={`font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>

      {/* Session Information and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <Card.Title>Quick Actions</Card.Title>
            </Card.Header>
            <Card.Content>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button fullWidth>Add Transaction</Button>
                <Button variant="outline" fullWidth>Create Budget</Button>
                <Button variant="outline" fullWidth>View Reports</Button>
                <Button variant="outline" fullWidth>Settings</Button>
              </div>
            </Card.Content>
          </Card>
        </div>
        
        <div>
          <SessionInfo compact showActions={false} />
        </div>
      </div>
    </div>
  );
} 