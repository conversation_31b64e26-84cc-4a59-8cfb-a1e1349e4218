'use client';

import { useState } from 'react';
import { useSession, signIn } from 'next-auth/react';
import { fundApi } from '@/lib/api';
import { Card, Button } from '@/components/ui';
import AuthDebug from '@/components/debug/AuthDebug';
import { performCompleteSignOut, performFreshSignIn } from '@/lib/auth-utils';

export default function TestAuthPage() {
  const { data: session, status } = useSession();
  const [apiResult, setApiResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAuthenticatedAPI = async () => {
    setLoading(true);
    setApiResult('');

    try {
      console.log('🧪 Testing authenticated API call...');
      console.log('Session:', session);

      // Check token expiration
      if (session?.expiresAt) {
        const now = new Date();
        const expiresAt = new Date(session.expiresAt * 1000);
        console.log('🕐 Current time:', now.toISOString());
        console.log('🕐 Token expires at:', expiresAt.toISOString());
        console.log('🕐 Token expired?', now > expiresAt);

        if (now > expiresAt) {
          setApiResult('Error: Access token has expired. Please refresh the page or sign in again.');
          return;
        }
      }

      const result = await fundApi.getFunds();
      console.log('✅ API call successful:', result);

      setApiResult(`Success: ${result.message}\nFunds count: ${result.data.length}`);

    } catch (error) {
      console.error('❌ API call failed:', error);
      setApiResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testDirectAPICall = async () => {
    setLoading(true);
    setApiResult('');

    try {
      console.log('🧪 Testing direct API call with session token...');
      console.log('🔐 Session data:', session);

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };

      if (session?.accessToken) {
        headers['Authorization'] = `Bearer ${session.accessToken}`;
        console.log('✅ Added Authorization header');
        console.log('🔑 Access token (first 50 chars):', session.accessToken.substring(0, 50) + '...');
      } else {
        console.warn('⚠️ No access token available');
      }

      console.log('📋 Request headers:', headers);

      const response = await fetch('https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds', {
        method: 'GET',
        headers,
        mode: 'cors',
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      const text = await response.text();
      console.log('📄 Response text:', text);

      setApiResult(`Status: ${response.status}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}\nResponse: ${text}`);

    } catch (error) {
      console.error('❌ Direct API call failed:', error);
      console.error('❌ Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });
      setApiResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}\nType: ${error instanceof Error ? error.name : 'Unknown'}`);
    } finally {
      setLoading(false);
    }
  };

  const testCORSPreflight = async () => {
    setLoading(true);
    setApiResult('');

    try {
      console.log('🧪 Testing CORS preflight...');

      const response = await fetch('https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds', {
        method: 'OPTIONS',
        headers: {
          'Origin': 'https://0fca-58-176-137-190.ngrok-free.app',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type,Authorization',
        },
      });

      console.log('📡 CORS Response status:', response.status);
      console.log('📡 CORS Response headers:', Object.fromEntries(response.headers.entries()));

      const text = await response.text();
      console.log('📄 CORS Response text:', text);

      setApiResult(`CORS Status: ${response.status}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}\nResponse: ${text}`);

    } catch (error) {
      console.error('❌ CORS test failed:', error);
      setApiResult(`CORS Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testSimpleRequest = async () => {
    setLoading(true);
    setApiResult('');

    try {
      console.log('🧪 Testing simple request without auth...');

      const response = await fetch('https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        mode: 'cors',
        credentials: 'omit',
      });

      console.log('📡 Simple Response status:', response.status);
      console.log('📡 Simple Response headers:', Object.fromEntries(response.headers.entries()));

      const text = await response.text();
      console.log('📄 Simple Response text:', text);

      setApiResult(`Simple Request Status: ${response.status}\nHeaders: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}\nResponse: ${text}`);

    } catch (error) {
      console.error('❌ Simple request failed:', error);
      console.error('❌ Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace',
      });
      setApiResult(`Simple Request Error: ${error instanceof Error ? error.message : 'Unknown error'}\nType: ${error instanceof Error ? error.name : 'Unknown'}`);
    } finally {
      setLoading(false);
    }
  };

  const testProxyAPI = async () => {
    setLoading(true);
    setApiResult('');

    try {
      console.log('🧪 Testing proxy API endpoint...');

      const response = await fetch('/api/proxy/funds', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      console.log('📡 Proxy Response status:', response.status);
      console.log('📡 Proxy Response headers:', Object.fromEntries(response.headers.entries()));

      const data = await response.json();
      console.log('📄 Proxy Response data:', data);

      setApiResult(`Proxy API Status: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`);

    } catch (error) {
      console.error('❌ Proxy API failed:', error);
      setApiResult(`Proxy API Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSignIn = () => {
    performFreshSignIn('cognito', '/test-auth');
  };

  const handleMockSignIn = () => {
    console.log('🎭 Mock sign-in clicked');
    console.log('🔧 Current environment variables:', {
      ENABLE_MOCK_MODE: process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE,
      ENABLE_MOCK_LOGIN: process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN,
      ENABLE_MOCK_FALLBACK: process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK,
      NEXTAUTH_URL: process.env.NEXTAUTH_URL
    });
    performFreshSignIn('mock-login', '/test-auth');
  };

  const handleSignOut = () => {
    performCompleteSignOut('/test-auth');
  };

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">Authentication Test</h1>

      {/* Debug Info */}
      <AuthDebug />

      {/* Authentication Status */}
      <Card>
        <Card.Header>
          <Card.Title>Authentication Status</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div>
              <strong>Status:</strong> {status}
            </div>
            
            {session ? (
              <div className="space-y-2">
                <div><strong>User:</strong> {session.user?.email}</div>
                <div><strong>Provider:</strong> {session.provider}</div>
                <div><strong>MFA Enabled:</strong> {session.mfaEnabled ? 'Yes' : 'No'}</div>
                <div><strong>Access Token:</strong> {session.accessToken ? 'Available' : 'Not available'}</div>
                {session.expiresAt && (
                  <div><strong>Expires At:</strong> {new Date(session.expiresAt * 1000).toLocaleString()}</div>
                )}
                
                <Button onClick={handleSignOut} variant="outline">
                  Sign Out
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <div>Not authenticated</div>
                <div className="flex gap-2">
                  <Button onClick={handleSignIn}>
                    Sign In with Cognito
                  </Button>
                  {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN === 'true' && (
                    <Button onClick={handleMockSignIn} variant="outline">
                      Mock Sign In (Dev)
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card.Content>
      </Card>

      {/* Environment Configuration */}
      <Card>
        <Card.Header>
          <Card.Title>Environment Configuration</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2 text-sm">
            <div><strong>ENABLE_MOCK_MODE:</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE}</div>
            <div><strong>USE_AWS_API:</strong> {process.env.NEXT_PUBLIC_USE_AWS_API}</div>
            <div><strong>API_BASE_URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL}</div>
            <div><strong>ENABLE_MOCK_FALLBACK (Legacy):</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK}</div>
            <div><strong>ENABLE_MOCK_LOGIN (Legacy):</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN}</div>
            <div><strong>AWS_REGION:</strong> {process.env.NEXT_PUBLIC_AWS_REGION}</div>
            <div><strong>NEXTAUTH_URL:</strong> {process.env.NEXTAUTH_URL || 'Not set (will use localhost)'}</div>
            <div><strong>COGNITO_CLIENT_ID:</strong> {process.env.COGNITO_CLIENT_ID ? 'Set' : 'Not set'}</div>
            <div><strong>COGNITO_ISSUER:</strong> {process.env.COGNITO_ISSUER ? 'Set' : 'Not set'}</div>
          </div>
        </Card.Content>
      </Card>

      {/* API Tests */}
      <Card>
        <Card.Header>
          <Card.Title>API Tests</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={testAuthenticatedAPI}
                disabled={loading}
                loading={loading}
              >
                Test Fund API (with auth)
              </Button>

              <Button
                onClick={testDirectAPICall}
                disabled={loading}
                loading={loading}
                variant="outline"
              >
                Test Direct API Call
              </Button>

              <Button
                onClick={testCORSPreflight}
                disabled={loading}
                loading={loading}
                variant="outline"
              >
                Test CORS Preflight
              </Button>

              <Button
                onClick={testSimpleRequest}
                disabled={loading}
                loading={loading}
                variant="outline"
              >
                Test Simple Request
              </Button>

              <Button
                onClick={testProxyAPI}
                disabled={loading}
                loading={loading}
                variant="outline"
              >
                Test Proxy API
              </Button>
            </div>
            
            {apiResult && (
              <div className="bg-gray-100 p-4 rounded">
                <h3 className="font-bold mb-2">API Test Result:</h3>
                <pre className="whitespace-pre-wrap text-sm">{apiResult}</pre>
              </div>
            )}
          </div>
        </Card.Content>
      </Card>

      {/* Instructions */}
      <Card>
        <Card.Header>
          <Card.Title>Instructions</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2 text-sm text-gray-600">
            <p>1. <strong>Sign in options:</strong></p>
            <ul className="ml-4 space-y-1">
              <li>• <strong>Cognito:</strong> Real authentication with AWS Cognito</li>
              {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN === 'true' && (
                <li>• <strong>Mock Sign In:</strong> Development-only authentication (any email/password works)</li>
              )}
            </ul>
            <p>2. <strong>Test API:</strong> After signing in, test the API calls</p>
            <p>3. <strong>Check console:</strong> Open browser console for detailed logs</p>
            <p>4. <strong>Mock modes:</strong></p>
            <ul className="ml-4 space-y-1">
              <li>• <strong>ENABLE_MOCK_FALLBACK:</strong> Use mock data when API fails</li>
              {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN === 'true' && (
                <li>• <strong>ENABLE_MOCK_LOGIN:</strong> Complete local testing without backend</li>
              )}
              {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN === 'true' && (
                <li>• <strong>URL Override:</strong> Mock login forces localhost URLs (no external dependencies)</li>
              )}
            </ul>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}
