'use client'

import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function TestNextAuth() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const testProviders = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/providers')
      const data = await response.json()
      setResult({ type: 'providers', data, status: response.status })
    } catch (error) {
      setResult({ type: 'providers', error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testSession = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/session')
      const data = await response.json()
      setResult({ type: 'session', data, status: response.status })
    } catch (error) {
      setResult({ type: 'session', error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testCSRF = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/auth/csrf')
      const data = await response.json()
      setResult({ type: 'csrf', data, status: response.status })
    } catch (error) {
      setResult({ type: 'csrf', error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testMockSignin = async () => {
    setLoading(true)
    try {
      // Test the mock signin endpoint directly
      const response = await fetch('/api/auth/signin/mock-login', {
        method: 'GET'
      })
      const text = await response.text()
      setResult({ 
        type: 'mock-signin', 
        data: text, 
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      })
    } catch (error) {
      setResult({ type: 'mock-signin', error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-2xl font-bold">NextAuth API Test</h1>
      
      <Card>
        <Card.Header>
          <Card.Title>API Endpoint Tests</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="flex gap-2 flex-wrap">
              <Button onClick={testProviders} disabled={loading}>
                Test /api/auth/providers
              </Button>
              <Button onClick={testSession} disabled={loading}>
                Test /api/auth/session
              </Button>
              <Button onClick={testCSRF} disabled={loading}>
                Test /api/auth/csrf
              </Button>
              <Button onClick={testMockSignin} disabled={loading}>
                Test /api/auth/signin/mock-login
              </Button>
            </div>
            
            {loading && (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span>Testing...</span>
              </div>
            )}
          </div>
        </Card.Content>
      </Card>

      {result && (
        <Card>
          <Card.Header>
            <Card.Title>Test Result: {result.type}</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-2">
              {result.status && (
                <div><strong>Status:</strong> {result.status}</div>
              )}
              {result.error && (
                <div className="text-red-600"><strong>Error:</strong> {result.error}</div>
              )}
              {result.data && (
                <div>
                  <strong>Response:</strong>
                  <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
                    {typeof result.data === 'string' 
                      ? result.data 
                      : JSON.stringify(result.data, null, 2)
                    }
                  </pre>
                </div>
              )}
              {result.headers && (
                <div>
                  <strong>Headers:</strong>
                  <pre className="mt-2 p-4 bg-gray-100 rounded text-sm overflow-auto">
                    {JSON.stringify(result.headers, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </Card.Content>
        </Card>
      )}

      <Card>
        <Card.Header>
          <Card.Title>Environment Info</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2 text-sm">
            <div><strong>NEXTAUTH_URL:</strong> {process.env.NEXTAUTH_URL || 'Not set'}</div>
            <div><strong>ENABLE_MOCK_LOGIN:</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN}</div>
            <div><strong>ENABLE_MOCK_FALLBACK:</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK}</div>
            <div><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.origin : 'Server-side'}</div>
          </div>
        </Card.Content>
      </Card>
    </div>
  )
}
