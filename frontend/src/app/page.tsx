'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useSession } from 'next-auth/react';
import { useTranslation } from '@/i18n/provider';

import AuthButton from '@/components/auth/AuthButton';
import {
  AccountBalance,
  Dashboard,
  Receipt,
  AccountBalanceWallet,
  Assessment,
  Security,
  Settings,
  WavingHand,
  ArrowForward
} from '@mui/icons-material';

export default function Home() {
  const { data: session, status } = useSession();
  const { t } = useTranslation();

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (session) {
    // If user is logged in, show dashboard preview
    return (
      <div className="max-w-6xl mx-auto">
        {/* Welcome Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <h1 className="text-4xl font-bold text-gray-900 mr-3">
              {t('home.welcomeBack', { name: session.user?.name || session.user?.email || '' })}
            </h1>
            <WavingHand className="text-4xl text-yellow-500" />
          </div>
          <p className="text-xl text-gray-600 mb-8">
            {t('home.readyToManage')}
          </p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            {t('home.goToDashboard')}
            <ArrowForward className="ml-2 -mr-1 w-5 h-5" />
          </Link>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
          <Link href="/dashboard" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200">
            <div className="text-center">
              <Dashboard className="text-3xl mb-3 text-blue-600 mx-auto" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('navigation.dashboard')}</h3>
              <p className="text-gray-600 text-sm">{t('home.quickActions.dashboard')}</p>
            </div>
          </Link>

          <Link href="/funds" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200">
            <div className="text-center">
              <AccountBalance className="text-3xl mb-3 text-indigo-600 mx-auto" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('navigation.funds')}</h3>
              <p className="text-gray-600 text-sm">{t('home.quickActions.funds')}</p>
            </div>
          </Link>

          <Link href="/portfolios" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200">
            <div className="text-center">
              <Assessment className="text-3xl mb-3 text-teal-600 mx-auto" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('navigation.portfolios')}</h3>
              <p className="text-gray-600 text-sm">{t('home.quickActions.portfolios')}</p>
            </div>
          </Link>

          <Link href="/transactions" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200">
            <div className="text-center">
              <Receipt className="text-3xl mb-3 text-green-600 mx-auto" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Transactions</h3>
              <p className="text-gray-600 text-sm">Manage your transactions</p>
            </div>
          </Link>

          <Link href="/budgets" className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200">
            <div className="text-center">
              <AccountBalanceWallet className="text-3xl mb-3 text-purple-600 mx-auto" sx={{ fontSize: 48 }} />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Budgets</h3>
              <p className="text-gray-600 text-sm">Track your spending goals</p>
            </div>
          </Link>
        </div>
      </div>
    );
  }

  // If user is not logged in, show landing page
  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="flex justify-center mb-6">
          <Image
            src="/logo-transparent.png"
            alt="FundFlow Logo"
            width={96}
            height={96}
            className="w-24 h-24"
          />
        </div>
        <h1 className="text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6">
          {t('home.title')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-400 mb-8 max-w-3xl mx-auto">
          {t('home.subtitle')}
        </p>
        <div className="flex justify-center">
          <AuthButton 
            variant="primary" 
            size="lg"
            redirectTo="/dashboard"
            className="px-8 py-4 text-lg"
          />
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <Dashboard className="text-4xl mb-4 text-blue-600 dark:text-blue-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.realTimeDashboard.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.realTimeDashboard.description')}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <Security className="text-4xl mb-4 text-green-600 dark:text-green-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.secureCompliant.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.secureCompliant.description')}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <Assessment className="text-4xl mb-4 text-purple-600 dark:text-purple-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.advancedAnalytics.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.advancedAnalytics.description')}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <Receipt className="text-4xl mb-4 text-orange-600 dark:text-orange-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.transactionManagement.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.transactionManagement.description')}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <AccountBalanceWallet className="text-4xl mb-4 text-indigo-600 dark:text-indigo-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.budgetPlanning.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.budgetPlanning.description')}
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
          <Settings className="text-4xl mb-4 text-gray-600 dark:text-gray-400" sx={{ fontSize: 64 }} />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3">{t('home.features.customizable.title')}</h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('home.features.customizable.description')}
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          {t('home.transformFinancial')}
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          {t('home.joinThousands')}
        </p>
        <AuthButton 
          variant="primary" 
          size="lg"
          redirectTo="/dashboard"
        />
      </div>
    </div>
  );
}
