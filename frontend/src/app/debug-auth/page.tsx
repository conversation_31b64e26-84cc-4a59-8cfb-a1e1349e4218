'use client'

import { getProviders } from 'next-auth/react'
import { useEffect, useState } from 'react'
import Card from '@/components/ui/Card'

export default function DebugAuth() {
  const [providers, setProviders] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadProviders() {
      try {
        console.log('🔍 Loading providers...')
        const providers = await getProviders()
        console.log('🔍 Loaded providers:', providers)
        setProviders(providers)
      } catch (error) {
        console.error('🔴 Error loading providers:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProviders()
  }, [])

  if (loading) {
    return (
      <div className="container mx-auto p-8">
        <h1 className="text-2xl font-bold mb-4">Loading Auth Debug...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-2xl font-bold">NextAuth Debug Information</h1>
      
      <Card>
        <Card.Header>
          <Card.Title>Environment Variables</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2 text-sm">
            <div><strong>ENABLE_MOCK_LOGIN:</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN}</div>
            <div><strong>ENABLE_MOCK_FALLBACK:</strong> {process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK}</div>
            <div><strong>NEXTAUTH_URL:</strong> {process.env.NEXTAUTH_URL || 'Not set'}</div>
            <div><strong>NODE_ENV:</strong> {process.env.NODE_ENV}</div>
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Available Providers</Card.Title>
        </Card.Header>
        <Card.Content>
          {providers ? (
            <div className="space-y-4">
              {Object.values(providers).map((provider: any) => (
                <div key={provider.id} className="border p-4 rounded">
                  <div><strong>ID:</strong> {provider.id}</div>
                  <div><strong>Name:</strong> {provider.name}</div>
                  <div><strong>Type:</strong> {provider.type}</div>
                  <div><strong>Sign-in URL:</strong> {provider.signinUrl}</div>
                  <div><strong>Callback URL:</strong> {provider.callbackUrl}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-red-600">No providers found</div>
          )}
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>NextAuth URLs</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2 text-sm">
            <div><strong>Base URL:</strong> {window.location.origin}</div>
            <div><strong>API Auth URL:</strong> {window.location.origin}/api/auth</div>
            <div><strong>Sign-in URL:</strong> {window.location.origin}/api/auth/signin</div>
            <div><strong>Sign-out URL:</strong> {window.location.origin}/api/auth/signout</div>
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>Quick Actions</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-2">
            <a 
              href="/api/auth/signin" 
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Go to Sign-in Page
            </a>
            <br />
            <a 
              href="/test-auth" 
              className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Go to Test Auth Page
            </a>
          </div>
        </Card.Content>
      </Card>
    </div>
  )
}
