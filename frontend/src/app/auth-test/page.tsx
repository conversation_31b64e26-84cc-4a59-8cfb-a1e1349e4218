'use client'

import { useSession } from 'next-auth/react'
import { useState } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { performCompleteSignOut, performFreshSignIn, checkForResidualAuthData } from '@/lib/auth-utils'

export default function AuthTestPage() {
  const { data: session, status } = useSession()
  const [residualData, setResidualData] = useState<any>(null)

  const handleCheckResidualData = () => {
    const data = checkForResidualAuthData()
    setResidualData(data)
  }

  const handleSignOut = () => {
    performCompleteSignOut('/')
  }

  const handleSignIn = () => {
    performFreshSignIn('cognito', '/auth-test')
  }

  return (
    <div className="container mx-auto p-8 space-y-6">
      <h1 className="text-3xl font-bold text-gray-900">Authentication Test Page</h1>
      
      <Card>
        <Card.Header>
          <h2 className="text-xl font-semibold">Current Authentication Status</h2>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div>
              <strong>Status:</strong> {status}
            </div>
            
            {session ? (
              <div className="space-y-2">
                <div><strong>Authenticated:</strong> Yes</div>
                <div><strong>Email:</strong> {session.user?.email}</div>
                <div><strong>Provider:</strong> {session.provider}</div>
                <div><strong>MFA Enabled:</strong> {session.mfaEnabled ? 'Yes' : 'No'}</div>
                <div><strong>Access Token:</strong> {session.accessToken ? 'Present' : 'Missing'}</div>
                {session.expiresAt && (
                  <div><strong>Expires At:</strong> {new Date(session.expiresAt * 1000).toLocaleString()}</div>
                )}
              </div>
            ) : (
              <div>
                <strong>Authenticated:</strong> No
              </div>
            )}
          </div>
        </Card.Content>
        <Card.Footer>
          <div className="flex space-x-3">
            {session ? (
              <Button onClick={handleSignOut} variant="destructive">
                Sign Out (Complete)
              </Button>
            ) : (
              <Button onClick={handleSignIn}>
                Sign In (Fresh)
              </Button>
            )}
          </div>
        </Card.Footer>
      </Card>

      <Card>
        <Card.Header>
          <h2 className="text-xl font-semibold">Residual Auth Data Check</h2>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <p>Check for any residual authentication data in browser storage:</p>
            
            <Button onClick={handleCheckResidualData} variant="outline">
              Check Residual Data
            </Button>
            
            {residualData && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold mb-2">Residual Data Found:</h3>
                <div className="space-y-2 text-sm">
                  <div>
                    <strong>localStorage items:</strong> {residualData.localStorage.length > 0 ? residualData.localStorage.join(', ') : 'None'}
                  </div>
                  <div>
                    <strong>sessionStorage items:</strong> {residualData.sessionStorage.length > 0 ? residualData.sessionStorage.join(', ') : 'None'}
                  </div>
                  <div>
                    <strong>Cookies:</strong> {residualData.cookies.length > 0 ? residualData.cookies.join(', ') : 'None'}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <h2 className="text-xl font-semibold">Test Instructions</h2>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <h3 className="font-semibold">To test the complete logout fix:</h3>
            <ol className="list-decimal list-inside space-y-2">
              <li>If you're currently signed in, click "Sign Out (Complete)"</li>
              <li>You will be redirected to Cognito logout page, then to a completion page</li>
              <li>Check for residual data - there should be none after complete logout</li>
              <li>Click "Sign In (Fresh)" - you should be redirected to Cognito login page</li>
              <li>Enter your credentials (you should NOT be automatically logged in)</li>
              <li>After successful login, you should be redirected back here</li>
            </ol>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800">Expected Behavior:</h4>
              <p className="text-blue-700">
                The complete logout process will:
              </p>
              <ul className="list-disc list-inside mt-2 text-blue-700 text-sm">
                <li>Clear all client-side storage and cookies</li>
                <li>Revoke access tokens on the server</li>
                <li>Clear NextAuth session</li>
                <li>Clear Cognito OAuth session</li>
                <li>Ensure email/user ID is completely cleared</li>
                <li>Force credential input on next sign-in</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800">Success Indicators:</h4>
              <ul className="list-disc list-inside text-green-700 text-sm">
                <li>No automatic login after logout</li>
                <li>Cognito login form appears every time</li>
                <li>No residual user data in browser storage</li>
                <li>Clean logout completion page is shown</li>
              </ul>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  )
}
