'use client';

import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setTheme, toggleTheme, updateSystemPreference, ThemeMode } from '@/store/slices/themeSlice';

export const useTheme = () => {
  const dispatch = useAppDispatch();
  const { mode, isDark } = useAppSelector((state) => state.theme);
  const [isInitialized, setIsInitialized] = useState(false);

  // Mark as initialized after first render
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    if (!isInitialized) return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = () => {
      dispatch(updateSystemPreference());
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [dispatch, isInitialized]);

  // Helper function to apply theme to DOM immediately
  const applyThemeToDOM = (shouldBeDark: boolean) => {
    const html = document.documentElement;
    const body = document.body;
    
    if (shouldBeDark) {
      html.classList.add('dark');
      body.className = body.className.replace(/bg-gray-\d+/g, '');
      body.classList.add('bg-gray-900');
    } else {
      html.classList.remove('dark');
      body.className = body.className.replace(/bg-gray-\d+/g, '');
      body.classList.add('bg-gray-50');
    }
    html.style.colorScheme = shouldBeDark ? 'dark' : 'light';
  };

  const changeTheme = (newMode: ThemeMode) => {
    console.log('changeTheme called with:', newMode);
    dispatch(setTheme(newMode));

    // Apply theme immediately for instant feedback
    const newIsDark = newMode === 'dark' || (newMode === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);
    applyThemeToDOM(newIsDark);
  };

  const toggle = () => {
    console.log('toggleTheme called, current state:', { mode, isDark });
    dispatch(toggleTheme());

    // Apply theme immediately for instant feedback
    let newMode: ThemeMode;
    if (mode === 'system') {
      newMode = isDark ? 'light' : 'dark';
    } else {
      newMode = mode === 'light' ? 'dark' : 'light';
    }
    const newIsDark = newMode === 'dark';
    applyThemeToDOM(newIsDark);
  };

  return {
    mode,
    isDark,
    isInitialized,
    setTheme: changeTheme,
    toggleTheme: toggle,
  };
};
