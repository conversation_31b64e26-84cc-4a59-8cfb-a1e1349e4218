// Global type definitions for FundFlow

export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  amount: number;
  description: string;
  category: string;
  date: Date;
  type: 'income' | 'expense';
  userId: string;
  tags?: string[];
}

export interface Budget {
  id: string;
  name: string;
  category: string;
  amount: number;
  spent: number;
  period: 'monthly' | 'weekly' | 'yearly';
  userId: string;
  startDate: Date;
  endDate: Date;
}

export interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
  type: 'income' | 'expense';
}

// Market Data Types
export type MarketDataSource = 'bloomberg' | 'reuters' | 'yahoo_finance' | 'alpha_vantage' | 'quandl' | 'manual_input' | 'fund_company' | 'regulatory_filing';
export type DataQuality = 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';

export interface MarketDataPoint {
  timestamp: string;
  value: number;
  source: MarketDataSource;
  quality: DataQuality;
  currency: string;
}

export interface PriceData {
  fundId: string;
  asOf: string;
  nav?: MarketDataPoint;
  marketPrice?: MarketDataPoint;
  bid?: MarketDataPoint;
  ask?: MarketDataPoint;
  openPrice?: MarketDataPoint;
  highPrice?: MarketDataPoint;
  lowPrice?: MarketDataPoint;
  closePrice?: MarketDataPoint;
  volume?: number;
  avgVolume30d?: number;
  bidAskSpread?: number;
  bidAskSpreadPct?: number;
  marketCap?: number;
  sharesOutstanding?: number;
  priceChange1d?: number;
  priceChange1dPct?: number;
  priceChangeYtd?: number;
  priceChangeYtdPct?: number;
}

export interface ValuationMetrics {
  fundId: string;
  asOf: string;
  priceToBook?: number;
  priceToEarnings?: number;
  priceToSales?: number;
  priceToCashFlow?: number;
  enterpriseValue?: number;
  evToRevenue?: number;
  evToEbitda?: number;
  returnOnEquity?: number;
  returnOnAssets?: number;
  returnOnInvestedCapital?: number;
  debtToEquity?: number;
  currentRatio?: number;
  quickRatio?: number;
  bookValuePerShare?: number;
  tangibleBookValue?: number;
  dividendYield?: number;
  dividendPayoutRatio?: number;
}

export interface TechnicalIndicators {
  fundId: string;
  asOf: string;
  sma20?: number;
  sma50?: number;
  sma200?: number;
  ema12?: number;
  ema26?: number;
  rsi14?: number;
  macdLine?: number;
  macdSignal?: number;
  macdHistogram?: number;
  bollingerUpper?: number;
  bollingerMiddle?: number;
  bollingerLower?: number;
  atr14?: number;
  vwap?: number;
  volumeSma20?: number;
  supportLevel?: number;
  resistanceLevel?: number;
}

export interface RiskAnalytics {
  fundId: string;
  asOf: string;
  var1d95?: number;
  var1d99?: number;
  var10d95?: number;
  var10d99?: number;
  cvar1d95?: number;
  cvar1d99?: number;
  sharpeRatio?: number;
  sortinoRatio?: number;
  calmarRatio?: number;
  informationRatio?: number;
  treynorRatio?: number;
  maxDrawdown?: number;
  maxDrawdownDuration?: number;
  currentDrawdown?: number;
  volatility1m?: number;
  volatility3m?: number;
  volatility1y?: number;
  downsideDeviation?: number;
  betaVsBenchmark?: number;
  correlationVsBenchmark?: number;
  trackingError?: number;
}

export interface Fund {
  id: string;
  name: string;
  symbol: string;
  type: 'mutual_fund' | 'etf' | 'index_fund' | 'bond_fund' | 'money_market' | 'equity' | 'bond' | 'mixed' | 'alternative' | 'index';
  category: string;
  subCategory?: string;
  nav: number; // Net Asset Value
  previousNav: number;
  change: number;
  changePercent: number;
  volume: number;
  aum: number; // Assets Under Management
  expenseRatio: number;
  minimumInvestment: number;
  riskLevel: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  rating: number; // 1-5 stars
  inceptionDate: string;
  fundManager: string;
  description: string;
  performance: {
    oneDay: number;
    oneWeek: number;
    oneMonth: number;
    threeMonths: number;
    sixMonths: number;
    oneYear: number;
    threeYears: number;
    fiveYears: number;
  };
  holdings?: {
    topHoldings?: {
      id?: string;
      name: string;
      symbol?: string;
      percentage: number;
      marketValue?: number;
      sector?: string;
      shares?: number;
    }[];
    sectorAllocation?: Record<string, number>;
    geographicAllocation?: Record<string, number>;
    assetAllocation?: Record<string, number>;
    marketCapAllocation?: Record<string, number>;
    currencyAllocation?: Record<string, number>;
    totalHoldingsCount?: number;
    holdingsConcentration?: number;
    lastUpdated?: string;
  };
  sectors?: {
    name: string;
    percentage: number;
  }[];

  // Enhanced market data
  currentPriceData?: PriceData;
  valuationMetrics?: ValuationMetrics;
  technicalIndicators?: TechnicalIndicators;
  riskAnalytics?: RiskAnalytics;

  createdAt: string;
  updatedAt: string;
}

// Market Data Input Interface
export interface MarketDataInput {
  fundId: string;
  inputTimestamp: Date;
  dataTimestamp: Date;
  inputBy: string;
  nav?: number;
  marketPrice?: number;
  volume?: number;
  priceToBook?: number;
  priceToEarnings?: number;
  dividendYield?: number;
  volatility?: number;
  beta?: number;
  notes?: string;
  validated: boolean;
  validationNotes?: string;
}

// Benchmark Data Interface
export interface BenchmarkData {
  benchmarkId: string;
  name: string;
  symbol: string;
  asOf: string;
  currentValue: number;
  return1d?: number;
  return1w?: number;
  return1m?: number;
  return3m?: number;
  return6m?: number;
  return1y?: number;
  return3y?: number;
  return5y?: number;
  volatility?: number;
  maxDrawdown?: number;
}

// Extended Fund Analytics Data for detailed view
export interface FundDetails extends Fund {
  analytics: {
    // Key Performance Indicators (Enhanced)
    kpis: {
      totalReturn: number;
      annualizedReturn: number;
      volatility: number;
      sharpeRatio: number;
      sortinoRatio: number;
      calmarRatio: number;
      informationRatio: number;
      treynorRatio: number;
      alpha: number;
      beta: number;
      maxDrawdown: number;
      trackingError: number;
    };

    // Risk Metrics (Enhanced)
    riskMetrics: {
      standardDeviation: number;
      downSideRisk: number;
      downsideDeviation: number;
      varRisk: number; // Value at Risk
      var1d95: number;
      var1d99: number;
      cvar1d95: number;
      cvar1d99: number;
      sortRatio: number;
      calmarRatio: number;
      correlation: number;
    };

    // Valuation Metrics
    valuationMetrics: {
      priceToBook: number;
      priceToEarnings: number;
      priceToSales: number;
      priceToCashFlow: number;
      enterpriseValue: number;
      evToRevenue: number;
      evToEbitda: number;
      returnOnEquity: number;
      returnOnAssets: number;
      debtToEquity: number;
      dividendYield: number;
      bookValuePerShare: number;
    };

    // Technical Indicators
    technicalIndicators: {
      sma20: number;
      sma50: number;
      sma200: number;
      rsi14: number;
      macdLine: number;
      macdSignal: number;
      bollingerUpper: number;
      bollingerLower: number;
      vwap: number;
      supportLevel: number;
      resistanceLevel: number;
    };

    // Asset Allocation
    assetAllocation: {
      stocks: number;
      bonds: number;
      cash: number;
      other: number;
    };

    // Geographic Allocation (dynamic regions from DynamoDB)
    geographicAllocation: {
      [region: string]: number;
    };

    // Market Cap Allocation
    marketCapAllocation: {
      largeCap: number;
      midCap: number;
      smallCap: number;
    };

    // Currency Allocation
    currencyAllocation: {
      [currency: string]: number;
    };

    // Top Holdings (expanded)
    topHoldings: {
      id: string;
      name: string;
      symbol: string;
      percentage: number;
      marketValue: number;
      sector: string;
      country?: string;
      currency?: string;
    }[];

    // Sector Allocation (expanded)
    sectorAllocation: {
      name: string;
      percentage: number;
      marketValue: number;
      change: number;
    }[];
  };
  
  // Historical performance data for charts
  historicalData: ChartDataPoint[];

  // Enhanced market data
  currentPriceData: PriceData;
  marketDataSummary: {
    lastUpdated: string;
    dataSources: { [field: string]: MarketDataSource };
    overallQuality: DataQuality;
  };

  // Benchmark comparison (Enhanced)
  primaryBenchmark: BenchmarkData;
  secondaryBenchmarks: BenchmarkData[];
  benchmark: {
    name: string;
    symbol: string;
    performance: {
      oneDay: number;
      oneWeek: number;
      oneMonth: number;
      threeMonths: number;
      sixMonths: number;
      oneYear: number;
      threeYears: number;
      fiveYears: number;
    };
  };

  // Fund documents and reports
  documents: {
    id: string;
    name: string;
    type: 'factsheet' | 'annual_report' | 'prospectus' | 'scheme_info';
    url: string;
    uploadDate: string;
  }[];

  // Market data input history
  marketDataInputs?: MarketDataInput[];
}

// Time period options for historical data
export type TimePeriod = '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | '3Y' | '5Y' | 'ALL';

// Chart data structure
export interface ChartDataPoint {
  date: string;
  value: number;
  nav?: number; // For fund NAV data
  volume?: number;
  returns?: number;
}

export interface FundPerformanceChart {
  timePeriod: TimePeriod;
  data: ChartDataPoint[];
  benchmarkData?: ChartDataPoint[];
}

// Fund comparison data
export interface FundComparison {
  funds: Fund[];
  metrics: {
    [fundId: string]: {
      returns: number;
      volatility: number;
      sharpeRatio: number;
      maxDrawdown: number;
    };
  };
  timePeriod: TimePeriod;
}

export interface FundFilter {
  search: string;
  type: Fund['type'] | '';
  category: string;
  riskLevel: Fund['riskLevel'] | '';
  minInvestment: number | null;
  maxInvestment: number | null;
  minRating: number | null;
  sortBy: 'name' | 'nav' | 'change' | 'volume' | 'aum' | 'expenseRatio' | 'rating';
  sortOrder: 'asc' | 'desc';
}

export interface FundState {
  funds: Fund[];
  filteredFunds: Fund[];
  loading: boolean;
  error: string | null;
  filter: FundFilter;
  selectedColumns: string[];
  lastUpdated: string | null;
}

// Common UI types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Portfolio Management Types
export type PortfolioStatus = 'active' | 'inactive' | 'closed' | 'liquidating';
export type PortfolioType = 'personal' | 'retirement' | 'taxable' | 'trust' | 'corporate' | 'education';
export type TransactionType = 'buy' | 'sell' | 'dividend' | 'interest' | 'fee' | 'transfer_in' | 'transfer_out' | 'split' | 'merger';

export interface PortfolioHolding {
  fundId: string;
  fundName: string;
  fundSymbol?: string;
  shares: number;
  averageCost: number;
  currentPrice: number;
  marketValue: number;
  costBasis: number;
  unrealizedGainLoss: number;
  unrealizedGainLossPct: number;
  weight: number; // Percentage weight in portfolio
  firstPurchaseDate: string;
  lastUpdated: string;
}

export interface PortfolioTransaction {
  transactionId: string;
  fundId: string;
  fundName: string;
  fundSymbol?: string;
  transactionType: TransactionType;
  transactionDate: string;
  settlementDate?: string;
  shares?: number;
  price?: number;
  amount: number;
  fees: number;
  netAmount: number;
  description?: string;
  referenceNumber?: string;
  createdAt: string;
}

export interface PortfolioPerformance {
  totalReturn: number;
  totalReturnPct: number;
  oneDayReturn?: number;
  oneWeekReturn?: number;
  oneMonthReturn?: number;
  threeMonthReturn?: number;
  sixMonthReturn?: number;
  oneYearReturn?: number;
  threeYearReturn?: number;
  fiveYearReturn?: number;
  inceptionReturn?: number;
  volatility?: number;
  sharpeRatio?: number;
  maxDrawdown?: number;
  benchmarkReturn?: number;
  alpha?: number;
  beta?: number;
  asOfDate: string;
}

export interface Portfolio {
  portfolioId: string;
  name: string;
  description?: string;
  portfolioType: PortfolioType;
  status: PortfolioStatus;
  userId: string;
  baseCurrency: string;
  inceptionDate: string;
  totalValue: number;
  totalCostBasis: number;
  cashBalance: number;
  totalGainLoss: number;
  totalGainLossPct: number;
  holdings: PortfolioHolding[];
  recentTransactions: PortfolioTransaction[];
  performance?: PortfolioPerformance;
  riskLevel?: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  benchmark?: string;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  lastRebalanced?: string;
  // Computed fields for UI
  holdingsCount?: number;
  transactionsCount?: number;
}

export interface PortfolioCreateRequest {
  name: string;
  description?: string;
  portfolioType: PortfolioType;
  baseCurrency?: string;
  inceptionDate?: string;
  cashBalance?: number;
  riskLevel?: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  benchmark?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface PortfolioUpdateRequest {
  name?: string;
  description?: string;
  portfolioType?: PortfolioType;
  status?: PortfolioStatus;
  baseCurrency?: string;
  cashBalance?: number;
  riskLevel?: 'very_low' | 'low' | 'moderate' | 'high' | 'very_high';
  benchmark?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface AddHoldingRequest {
  fundId: string;
  shares: number;
  purchasePrice: number;
  purchaseDate: string;
  fees?: number;
}

export interface AddTransactionRequest {
  fundId: string;
  transactionType: TransactionType;
  transactionDate: string;
  shares?: number;
  price?: number;
  amount: number;
  fees?: number;
  description?: string;
  referenceNumber?: string;
}

export interface PortfolioFilter {
  search: string;
  portfolioType: PortfolioType | '';
  status: PortfolioStatus | '';
  riskLevel: Portfolio['riskLevel'] | '';
  minValue: number | null;
  maxValue: number | null;
  sortBy: 'name' | 'totalValue' | 'totalGainLossPct' | 'createdAt' | 'updatedAt';
  sortOrder: 'asc' | 'desc';
}

export interface PortfolioState {
  portfolios: Portfolio[];
  filteredPortfolios: Portfolio[];
  currentPortfolio: Portfolio | null;
  loading: boolean;
  error: string | null;
  filter: PortfolioFilter;
  lastUpdated: string | null;
}

// Portfolio Analytics Types
export interface PortfolioAnalytics {
  assetAllocation: {
    [assetClass: string]: {
      value: number;
      percentage: number;
      funds: string[];
    };
  };
  sectorAllocation: {
    [sector: string]: {
      value: number;
      percentage: number;
      funds: string[];
    };
  };
  geographicAllocation: {
    [region: string]: {
      value: number;
      percentage: number;
      funds: string[];
    };
  };
  riskMetrics: {
    portfolioVolatility: number;
    portfolioBeta: number;
    portfolioSharpe: number;
    concentrationRisk: number;
    diversificationRatio: number;
  };
  performanceAttribution: {
    assetAllocation: number;
    securitySelection: number;
    interaction: number;
    total: number;
  };
}

export interface PortfolioComparison {
  portfolios: Portfolio[];
  metrics: {
    [portfolioId: string]: {
      totalReturn: number;
      volatility: number;
      sharpeRatio: number;
      maxDrawdown: number;
      beta: number;
    };
  };
  timePeriod: TimePeriod;
}

// Portfolio Chart Data
export interface PortfolioChartData {
  timePeriod: TimePeriod;
  data: ChartDataPoint[];
  benchmarkData?: ChartDataPoint[];
  cashFlowData?: ChartDataPoint[];
}

export interface PortfolioAllocationChart {
  type: 'asset' | 'sector' | 'geographic' | 'fund';
  data: {
    name: string;
    value: number;
    percentage: number;
    color?: string;
  }[];
}