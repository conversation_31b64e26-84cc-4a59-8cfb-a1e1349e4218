import NextAuth from "next-auth"
import CognitoProvider from "next-auth/providers/cognito"
import CredentialsProvider from "next-auth/providers/credentials"
import type { Account, User } from "next-auth"
import type { JWT } from "next-auth/jwt"
import type { NextAuthOptions } from "next-auth"
import https from "https"

declare module "next-auth" {
  interface Session {
    accessToken?: string
    expiresAt?: number
    provider?: string
    mfaEnabled?: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    accessToken?: string
    refreshToken?: string
    expiresAt?: number
    provider?: string
    mfaEnabled?: boolean
  }
}

// Create custom HTTPS agent for development
const createHttpsAgent = () => {
  if (process.env.NODE_ENV === 'development') {
    return new https.Agent({
      rejectUnauthorized: false,
      timeout: 10000,
    })
  }
  return new https.Agent({
    timeout: 10000,
  })
}

// Consolidated Mock Mode Configuration
// Set NEXT_PUBLIC_ENABLE_MOCK_MODE=true to enable complete mock mode (login, API fallback, and data)
// Set NEXT_PUBLIC_ENABLE_MOCK_MODE=false to use real AWS services
const ENABLE_MOCK_MODE = process.env.NEXT_PUBLIC_ENABLE_MOCK_MODE === 'true'

// Legacy support for existing flags (will be deprecated)
const LEGACY_ENABLE_MOCK_LOGIN = process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN === 'true'
const LEGACY_ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true'

// Determine actual behavior based on new flag or legacy flags
const ENABLE_MOCK_LOGIN = ENABLE_MOCK_MODE ? true : LEGACY_ENABLE_MOCK_LOGIN
const ENABLE_MOCK_FALLBACK = ENABLE_MOCK_MODE ? true : LEGACY_ENABLE_MOCK_FALLBACK

console.log('🔧 Auth Configuration:', {
  ENABLE_MOCK_MODE,
  ENABLE_MOCK_LOGIN,
  ENABLE_MOCK_FALLBACK,
  NODE_ENV: process.env.NODE_ENV,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  // Legacy flags (for debugging)
  LEGACY_ENABLE_MOCK_LOGIN,
  LEGACY_ENABLE_MOCK_FALLBACK
})

// For Cognito, we need to use the domain-based URLs for OAuth endpoints
const cognitoDomain = 'ap-northeast-1h2kkhguat.auth.ap-northeast-1.amazoncognito.com'
const cognitoIssuer = process.env.COGNITO_ISSUER!

// Function to refresh access token using refresh token
async function refreshAccessToken(refreshToken: string) {
  try {
    console.log('🔄 Refreshing access token...');

    const response = await fetch(`https://${cognitoDomain}/oauth2/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: refreshToken,
        client_id: process.env.COGNITO_CLIENT_ID!,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Token refresh failed:', response.status, errorText);
      throw new Error(`Token refresh failed: ${response.status} ${errorText}`);
    }

    const tokens = await response.json();
    console.log('✅ Token refresh successful');

    return {
      access_token: tokens.access_token,
      expires_in: tokens.expires_in || 3600, // Default to 1 hour if not provided
    };
  } catch (error) {
    console.error('❌ Error refreshing token:', error);
    throw error;
  }
}

const cognitoProviderOptions = {
  clientId: process.env.COGNITO_CLIENT_ID!,
  clientSecret: process.env.COGNITO_CLIENT_SECRET || '',
  client: { token_endpoint_auth_method: "none" as const },
  issuer: cognitoIssuer,
  checks: ['pkce', 'state'] as ('pkce' | 'state')[],
  authorization: {
    url: `https://${cognitoDomain}/oauth2/authorize`,
    params: {
      scope: 'openid profile email aws.cognito.signin.user.admin',
      response_type: 'code',
    },
  },
  token: `https://${cognitoDomain}/oauth2/token`,
  userinfo: `https://${cognitoDomain}/oauth2/userInfo`,
  jwks_uri: `${cognitoIssuer}/.well-known/jwks.json`,
  httpOptions: {
    timeout: 10000,
    agent: createHttpsAgent(),
  },
};

// For public clients, the clientSecret parameter must be omitted entirely from
// the token request. We delete the key to ensure it's not sent.
if (!process.env.COGNITO_CLIENT_SECRET) {
  delete (cognitoProviderOptions as { clientSecret?: string }).clientSecret;
}

// Create providers array based on configuration
const createProviders = () => {
  const providers = []

  // Always add Cognito provider for production use
  if (!ENABLE_MOCK_LOGIN || process.env.NODE_ENV === 'production') {
    providers.push(CognitoProvider(cognitoProviderOptions))
  }

  // Add mock credentials provider for local development
  if (ENABLE_MOCK_LOGIN && ENABLE_MOCK_FALLBACK) {
    console.log('🎭 Adding mock credentials provider')

    // Create a simple mock provider
    const mockProvider = {
      id: 'mock-login',
      name: 'Mock Login (Development Only)',
      type: 'credentials',
      credentials: {
        email: {
          label: 'Email',
          type: 'email',
          placeholder: '<EMAIL>'
        },
        password: {
          label: 'Password',
          type: 'password',
          placeholder: 'password'
        }
      },
      async authorize(credentials: any) {
        console.log('🎭 Mock authorize called')

        // Always return a valid user in mock mode
        return {
          id: 'mock-user-123',
          email: credentials?.email || '<EMAIL>',
          name: 'Mock User',
        }
      }
    }

    providers.push(CredentialsProvider(mockProvider))
  }

  console.log('🔧 Created providers:', providers.map(p => ({ id: p.id, name: p.name })))
  return providers
}



export const authOptions: NextAuthOptions = {
  providers: createProviders(),

  // Ensure we have a secret
  secret: process.env.NEXTAUTH_SECRET,

  // Add some debugging
  logger: {
    error(code, metadata) {
      console.error('🔴 NextAuth Error:', code, metadata)
    },
    warn(code) {
      console.warn('🟡 NextAuth Warning:', code)
    },
    debug(code, metadata) {
      console.log('🔍 NextAuth Debug:', code, metadata)
    }
  },

  callbacks: {
    async jwt({ token, user, account, trigger }: { token: JWT; user: User; account: Account | null; trigger?: string }) {
      // Handle mock login
      if (account?.provider === 'mock-login') {
        console.log('🎭 Setting up mock JWT token')

        // Generate mock token data
        const mockExpiresAt = Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours from now

        token.accessToken = `mock-access-token-${Date.now()}`
        token.expiresAt = mockExpiresAt
        token.provider = 'mock-login'
        token.mfaEnabled = false

        return token
      }

      // Only store essential data to reduce cookie size for real Cognito tokens
      if (account && account.provider !== 'mock-login') {
        // Store only the most essential token info
        token.accessToken = account.access_token
        token.expiresAt = account.expires_at
        token.provider = account.provider

        // Store refresh token for token refresh
        token.refreshToken = account.refresh_token

        // Minimal MFA check without storing full token
        if (account.id_token) {
          try {
            const idTokenPayload = JSON.parse(atob(account.id_token.split('.')[1]))
            token.mfaEnabled = !!idTokenPayload.auth_time
          } catch (error) {
            console.warn('Failed to parse ID token for MFA info:', error)
            token.mfaEnabled = false
          }
        }
      }

      // TODO: Re-enable token refresh logic after fixing the sign-in issue
      // For now, let expired tokens be handled by the API layer
      // if (token.accessToken && token.expiresAt && Date.now() >= token.expiresAt * 1000) {
      //   console.log('🔄 Access token expired, attempting refresh...');
      //   // ... refresh logic
      // }

      return token
    },
    async session({ session, token }: { session: any; token: JWT }) {
      // Ensure session object exists
      if (!session) {
        return null;
      }

      // Only send minimal essential properties to reduce cookie size
      session.accessToken = token.accessToken
      session.expiresAt = token.expiresAt
      session.provider = token.provider
      session.mfaEnabled = token.mfaEnabled

      // Remove any large user properties to reduce cookie size
      if (session.user) {
        // Keep only essential user info
        session.user = {
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
        }
      }

      return session
    },
    async signIn({ user, account, profile }: { user: User; account: Account | null; profile?: any }) {
      console.log('🔐 SignIn callback called:', {
        provider: account?.provider,
        userId: user?.id,
        userEmail: user?.email
      })

      // Allow mock login in development
      if (account?.provider === 'mock-login') {
        console.log('🎭 Mock login sign-in callback - allowing sign in')
        return true
      }

      // Allow Cognito login
      if (account?.provider === 'cognito') {
        console.log('🔐 Cognito login sign-in callback - allowing sign in')
        return true
      }

      console.log('🔐 Unknown provider, allowing sign in by default')
      return true
    },
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 24 * 60 * 60, // 24 hours
  },
  jwt: {
    maxAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60, // 24 hours
      },
    },
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
  },

  // Add custom error handling
  events: {
    async signIn(message) {
      console.log('🔍 NextAuth signIn event:', message)
    },
    async signOut(message) {
      console.log('🔍 NextAuth signOut event:', message)
    },
    async createUser(message) {
      console.log('🔍 NextAuth createUser event:', message)
    },
    async session(message) {
      console.log('🔍 NextAuth session event:', message)
    }
  },

  debug: process.env.NODE_ENV === "development",
}

// Create NextAuth instance
const handler = NextAuth(authOptions)

// Export handlers for API routes
export { handler as GET, handler as POST }

// For backward compatibility and other uses
export const auth = handler
export const signIn = handler
export const signOut = handler

// Export handlers for the new App Router syntax
export const handlers = { GET: handler, POST: handler }