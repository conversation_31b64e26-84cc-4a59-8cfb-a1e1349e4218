/**
 * SSL Configuration Module for Node.js
 * 
 * This module provides centralized SSL certificate handling to resolve
 * UNABLE_TO_GET_ISSUER_CERT_LOCALLY errors when connecting to AWS services.
 */

import https from 'https';
import fs from 'fs';
import path from 'path';

// Environment variables for SSL configuration
const SSL_CONFIG = {
  // Path to custom CA bundle (if provided)
  CA_BUNDLE_PATH: process.env.SSL_CA_BUNDLE_PATH,
  
  // Whether to use system CA certificates
  USE_SYSTEM_CA: process.env.SSL_USE_SYSTEM_CA !== 'false',
  
  // Whether to reject unauthorized certificates (should be true in production)
  REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0',
  
  // Timeout for SSL connections
  TIMEOUT: parseInt(process.env.SSL_TIMEOUT || '10000'),
  
  // Whether we're in development mode
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
};

/**
 * Load custom CA certificates from file or environment
 */
function loadCustomCACertificates(): string[] {
  const caCerts: string[] = [];
  
  // Load from custom CA bundle file if specified
  if (SSL_CONFIG.CA_BUNDLE_PATH && fs.existsSync(SSL_CONFIG.CA_BUNDLE_PATH)) {
    try {
      const caBundleContent = fs.readFileSync(SSL_CONFIG.CA_BUNDLE_PATH, 'utf8');
      console.log(`✅ Loaded custom CA bundle from: ${SSL_CONFIG.CA_BUNDLE_PATH}`);
      caCerts.push(caBundleContent);
    } catch (error) {
      console.warn(`⚠️ Failed to load CA bundle from ${SSL_CONFIG.CA_BUNDLE_PATH}:`, error);
    }
  }
  
  // Load from environment variable if provided
  if (process.env.SSL_CA_CERT) {
    caCerts.push(process.env.SSL_CA_CERT);
    console.log('✅ Loaded CA certificate from environment variable');
  }
  
  return caCerts;
}

/**
 * Create an HTTPS agent with proper SSL configuration
 */
export function createSecureHttpsAgent(): https.Agent {
  const customCACerts = loadCustomCACertificates();
  
  const agentOptions: https.AgentOptions = {
    timeout: SSL_CONFIG.TIMEOUT,
    keepAlive: true,
    maxSockets: 50,
    maxFreeSockets: 10,
  };
  
  // In development, we might need to be more lenient with certificates
  if (SSL_CONFIG.IS_DEVELOPMENT) {
    console.log('🔧 Development mode: Configuring SSL for development environment');
    
    // Only reject unauthorized if explicitly set to do so
    agentOptions.rejectUnauthorized = SSL_CONFIG.REJECT_UNAUTHORIZED;
    
    if (!SSL_CONFIG.REJECT_UNAUTHORIZED) {
      console.warn('⚠️ SSL certificate verification is DISABLED in development mode');
    }
  } else {
    // In production, always verify certificates
    agentOptions.rejectUnauthorized = true;
    console.log('🔒 Production mode: SSL certificate verification is ENABLED');
  }
  
  // Add custom CA certificates if available
  if (customCACerts.length > 0) {
    agentOptions.ca = customCACerts;
    console.log(`✅ Using ${customCACerts.length} custom CA certificate(s)`);
  }
  
  return new https.Agent(agentOptions);
}

/**
 * Create fetch options with proper SSL configuration
 */
export function createSecureFetchOptions(): RequestInit {
  const agent = createSecureHttpsAgent();
  
  return {
    // @ts-ignore - Node.js specific agent option
    agent: agent,
    timeout: SSL_CONFIG.TIMEOUT,
  };
}

/**
 * Enhanced fetch function with SSL configuration
 */
export async function secureFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const secureOptions = createSecureFetchOptions();
  
  // Merge the secure options with user-provided options
  const mergedOptions: RequestInit = {
    ...secureOptions,
    ...options,
    headers: {
      ...secureOptions.headers,
      ...options.headers,
    },
  };
  
  try {
    console.log(`🔗 Making secure request to: ${url}`);
    const response = await fetch(url, mergedOptions);
    console.log(`✅ Secure request completed with status: ${response.status}`);
    return response;
  } catch (error) {
    console.error(`❌ Secure request failed for ${url}:`, error);
    
    // Provide helpful error messages for common SSL issues
    if (error instanceof Error) {
      if (error.message.includes('UNABLE_TO_GET_ISSUER_CERT_LOCALLY')) {
        console.error('💡 SSL Certificate Issue: The server certificate cannot be verified.');
        console.error('   This usually happens when:');
        console.error('   1. The certificate chain is incomplete');
        console.error('   2. Custom CA certificates are needed');
        console.error('   3. Corporate firewall is intercepting SSL traffic');
        console.error('   4. System CA certificates are outdated');
      } else if (error.message.includes('CERT_HAS_EXPIRED')) {
        console.error('💡 SSL Certificate Issue: The server certificate has expired.');
      } else if (error.message.includes('CERT_UNTRUSTED')) {
        console.error('💡 SSL Certificate Issue: The server certificate is not trusted.');
      }
    }
    
    throw error;
  }
}

/**
 * Configure global SSL settings for Node.js
 */
export function configureGlobalSSL(): void {
  console.log('🔧 Configuring global SSL settings...');
  
  // Set global SSL configuration
  if (SSL_CONFIG.IS_DEVELOPMENT && !SSL_CONFIG.REJECT_UNAUTHORIZED) {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    console.warn('⚠️ Global SSL certificate verification is DISABLED for development');
  }
  
  // Load custom CA certificates into Node.js
  const customCACerts = loadCustomCACertificates();
  if (customCACerts.length > 0) {
    // Note: This requires the 'tls' module for more advanced configuration
    console.log('✅ Custom CA certificates loaded for global use');
  }
  
  console.log('✅ Global SSL configuration completed');
}

/**
 * Validate SSL configuration and provide diagnostics
 */
export function validateSSLConfiguration(): void {
  console.log('🔍 Validating SSL configuration...');
  
  console.log('SSL Configuration:');
  console.log(`  - Environment: ${SSL_CONFIG.IS_DEVELOPMENT ? 'Development' : 'Production'}`);
  console.log(`  - Reject Unauthorized: ${SSL_CONFIG.REJECT_UNAUTHORIZED}`);
  console.log(`  - Timeout: ${SSL_CONFIG.TIMEOUT}ms`);
  console.log(`  - Use System CA: ${SSL_CONFIG.USE_SYSTEM_CA}`);
  console.log(`  - Custom CA Bundle Path: ${SSL_CONFIG.CA_BUNDLE_PATH || 'Not specified'}`);
  
  // Check if custom CA bundle exists
  if (SSL_CONFIG.CA_BUNDLE_PATH) {
    if (fs.existsSync(SSL_CONFIG.CA_BUNDLE_PATH)) {
      console.log(`  ✅ Custom CA bundle found at: ${SSL_CONFIG.CA_BUNDLE_PATH}`);
    } else {
      console.log(`  ❌ Custom CA bundle NOT found at: ${SSL_CONFIG.CA_BUNDLE_PATH}`);
    }
  }
  
  // Check Node.js version for SSL support
  const nodeVersion = process.version;
  console.log(`  - Node.js Version: ${nodeVersion}`);
  
  console.log('✅ SSL configuration validation completed');
}

// Export configuration for external use
export { SSL_CONFIG };
