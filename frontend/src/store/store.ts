import { configureStore } from '@reduxjs/toolkit';
import counterSlice from './slices/counterSlice';
import fundsSlice from './slices/fundsSlice';
import portfoliosSlice from './slices/portfoliosSlice';
import themeSlice from './slices/themeSlice';

export const store = configureStore({
  reducer: {
    counter: counterSlice,
    funds: fundsSlice,
    portfolios: portfoliosSlice,
    theme: themeSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 