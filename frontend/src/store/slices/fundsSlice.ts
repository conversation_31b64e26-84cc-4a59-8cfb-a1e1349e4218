import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Fund, FundState, FundFilter } from '@/types';
import { fundApi } from '@/lib/api';

// Initial filter state
const initialFilter: FundFilter = {
  search: '',
  type: '',
  category: '',
  riskLevel: '',
  minInvestment: null,
  maxInvestment: null,
  minRating: null,
  sortBy: 'name',
  sortOrder: 'asc',
};

// Load persisted column preferences from localStorage
const loadPersistedColumns = (): string[] => {
  if (typeof window !== 'undefined') {
    try {
      const saved = localStorage.getItem('fundflow-selected-columns');
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.warn('Failed to load column preferences from localStorage:', error);
    }
  }
  return ['name', 'nav', 'change', 'changePercent', 'volume', 'rating'];
};

// Save column preferences to localStorage
const savePersistedColumns = (columns: string[]) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('fundflow-selected-columns', JSON.stringify(columns));
    } catch (error) {
      console.warn('Failed to save column preferences to localStorage:', error);
    }
  }
};

// Initial state
const initialState: FundState = {
  funds: [],
  filteredFunds: [],
  loading: false,
  error: null,
  filter: initialFilter,
  selectedColumns: loadPersistedColumns(),
  lastUpdated: null,
};

// Async thunks
export const fetchFunds = createAsyncThunk(
  'funds/fetchFunds',
  async (pageSize: number = 100, { rejectWithValue }) => {
    try {
      console.log('🚀 Redux fetchFunds: Starting API call...');
      const response = await fundApi.getFunds(pageSize);
      console.log('📡 Redux fetchFunds: API response received:', response);

      if (response.success) {
        console.log('✅ Redux fetchFunds: Success, returning data:', response.data?.length, 'funds');
        return response.data;
      } else {
        console.error('❌ Redux fetchFunds: API returned success=false:', response.message);
        return rejectWithValue(response.message);
      }
    } catch (error) {
      console.error('❌ Redux fetchFunds: Exception caught:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch funds');
    }
  }
);

export const searchFunds = createAsyncThunk(
  'funds/searchFunds',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await fundApi.searchFunds(query);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to search funds');
    }
  }
);

export const fetchFundById = createAsyncThunk(
  'funds/fetchFundById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await fundApi.getFundById(id);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch fund');
    }
  }
);

// Helper functions for filtering and sorting
const applyFilters = (funds: Fund[], filter: FundFilter): Fund[] => {
  console.log('🔍 applyFilters: Input funds:', funds?.length, 'filter:', filter);
  
  if (!funds || !Array.isArray(funds)) {
    console.error('❌ applyFilters: Invalid funds input:', funds);
    return [];
  }
  
  let filtered = [...funds];

  // Apply search filter
  if (filter.search.trim()) {
    const searchLower = filter.search.toLowerCase();
    filtered = filtered.filter(fund =>
      fund.name.toLowerCase().includes(searchLower) ||
      fund.symbol.toLowerCase().includes(searchLower) ||
      fund.category.toLowerCase().includes(searchLower) ||
      fund.fundManager.toLowerCase().includes(searchLower)
    );
  }

  // Apply type filter
  if (filter.type) {
    filtered = filtered.filter(fund => fund.type === filter.type);
  }

  // Apply category filter
  if (filter.category) {
    filtered = filtered.filter(fund => fund.category === filter.category);
  }

  // Apply risk level filter
  if (filter.riskLevel) {
    filtered = filtered.filter(fund => fund.riskLevel === filter.riskLevel);
  }

  // Apply minimum investment filter
  if (filter.minInvestment !== null) {
    filtered = filtered.filter(fund => fund.minimumInvestment >= filter.minInvestment!);
  }

  if (filter.maxInvestment !== null) {
    filtered = filtered.filter(fund => fund.minimumInvestment <= filter.maxInvestment!);
  }

  // Apply rating filter
  if (filter.minRating !== null) {
    filtered = filtered.filter(fund => fund.rating >= filter.minRating!);
  }

  // Apply sorting
  filtered.sort((a, b) => {
    let aValue: any = a[filter.sortBy];
    let bValue: any = b[filter.sortBy];

    // Handle string sorting
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    let comparison = 0;
    if (aValue > bValue) {
      comparison = 1;
    } else if (aValue < bValue) {
      comparison = -1;
    }

    return filter.sortOrder === 'desc' ? comparison * -1 : comparison;
  });

  console.log('✅ applyFilters: Returning filtered funds:', filtered.length);
  return filtered;
};

// Slice
const fundsSlice = createSlice({
  name: 'funds',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<Partial<FundFilter>>) => {
      state.filter = { ...state.filter, ...action.payload };
      state.filteredFunds = applyFilters(state.funds, state.filter);
    },
    resetFilter: (state) => {
      state.filter = initialFilter;
      state.filteredFunds = [...state.funds];
    },
    setSelectedColumns: (state, action: PayloadAction<string[]>) => {
      state.selectedColumns = action.payload;
      savePersistedColumns(action.payload);
    },
    toggleColumn: (state, action: PayloadAction<string>) => {
      const column = action.payload;
      if (state.selectedColumns.includes(column)) {
        state.selectedColumns = state.selectedColumns.filter(col => col !== column);
      } else {
        state.selectedColumns = [...state.selectedColumns, column];
      }
      savePersistedColumns(state.selectedColumns);
    },
    setSortBy: (state, action: PayloadAction<{ sortBy: FundFilter['sortBy']; sortOrder?: FundFilter['sortOrder'] }>) => {
      const { sortBy, sortOrder } = action.payload;
      
      // If clicking the same column, toggle sort order
      if (state.filter.sortBy === sortBy && !sortOrder) {
        state.filter.sortOrder = state.filter.sortOrder === 'asc' ? 'desc' : 'asc';
      } else {
        state.filter.sortBy = sortBy;
        state.filter.sortOrder = sortOrder || 'asc';
      }
      
      state.filteredFunds = applyFilters(state.funds, state.filter);
    },
    clearError: (state) => {
      state.error = null;
    },
    updateFund: (state, action: PayloadAction<Fund>) => {
      const updatedFund = action.payload;
      const index = state.funds.findIndex(fund => fund.id === updatedFund.id);
      if (index !== -1) {
        state.funds[index] = updatedFund;
        state.filteredFunds = applyFilters(state.funds, state.filter);
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch funds
    builder
      .addCase(fetchFunds.pending, (state) => {
        console.log('🔄 Redux: fetchFunds.pending - Setting loading=true');
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFunds.fulfilled, (state, action) => {
        console.log('✅ Redux: fetchFunds.fulfilled - Received data:', action.payload?.length, 'funds');
        console.log('📊 Redux: Sample fund data:', action.payload?.[0]);
        state.loading = false;
        state.funds = action.payload;
        state.filteredFunds = applyFilters(action.payload, state.filter);
        state.lastUpdated = new Date().toISOString();
        console.log('✅ Redux: State updated - funds:', state.funds.length, 'filteredFunds:', state.filteredFunds.length);
      })
      .addCase(fetchFunds.rejected, (state, action) => {
        console.error('❌ Redux: fetchFunds.rejected - Error:', action.payload);
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Search funds
      .addCase(searchFunds.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchFunds.fulfilled, (state, action) => {
        state.loading = false;
        state.filteredFunds = action.payload;
      })
      .addCase(searchFunds.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Fetch fund by ID
      .addCase(fetchFundById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchFundById.fulfilled, (state, action) => {
        state.loading = false;
        const fund = action.payload;
        const existingIndex = state.funds.findIndex(f => f.id === fund.id);
        if (existingIndex !== -1) {
          state.funds[existingIndex] = fund;
        } else {
          state.funds.push(fund);
        }
        state.filteredFunds = applyFilters(state.funds, state.filter);
      })
      .addCase(fetchFundById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setFilter,
  resetFilter,
  setSelectedColumns,
  toggleColumn,
  setSortBy,
  clearError,
  updateFund,
} = fundsSlice.actions;

export default fundsSlice.reducer; 