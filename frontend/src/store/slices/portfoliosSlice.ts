import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Portfolio, PortfolioState, PortfolioFilter, PortfolioCreateRequest, PortfolioUpdateRequest, AddHoldingRequest, AddTransactionRequest } from '@/types';
import { portfolioApi } from '@/lib/api';

// Initial filter state
const initialFilter: PortfolioFilter = {
  search: '',
  portfolioType: '',
  status: '',
  riskLevel: '',
  minValue: null,
  maxValue: null,
  sortBy: 'name',
  sortOrder: 'asc',
};

// Initial state
const initialState: PortfolioState = {
  portfolios: [],
  filteredPortfolios: [],
  currentPortfolio: null,
  loading: false,
  error: null,
  filter: initialFilter,
  lastUpdated: null,
};

// Helper function to apply filters
const applyFilters = (portfolios: Portfolio[], filter: PortfolioFilter): Portfolio[] => {
  return portfolios.filter(portfolio => {
    // Search filter
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      const matchesSearch = 
        portfolio.name.toLowerCase().includes(searchLower) ||
        portfolio.description?.toLowerCase().includes(searchLower) ||
        portfolio.tags.some(tag => tag.toLowerCase().includes(searchLower));
      if (!matchesSearch) return false;
    }

    // Portfolio type filter
    if (filter.portfolioType && portfolio.portfolioType !== filter.portfolioType) {
      return false;
    }

    // Status filter
    if (filter.status && portfolio.status !== filter.status) {
      return false;
    }

    // Risk level filter
    if (filter.riskLevel && portfolio.riskLevel !== filter.riskLevel) {
      return false;
    }

    // Value range filters
    if (filter.minValue !== null && portfolio.totalValue < filter.minValue) {
      return false;
    }
    if (filter.maxValue !== null && portfolio.totalValue > filter.maxValue) {
      return false;
    }

    return true;
  }).sort((a, b) => {
    const { sortBy, sortOrder } = filter;
    let aValue: any, bValue: any;

    switch (sortBy) {
      case 'name':
        aValue = a.name;
        bValue = b.name;
        break;
      case 'totalValue':
        aValue = a.totalValue;
        bValue = b.totalValue;
        break;
      case 'totalGainLossPct':
        aValue = a.totalGainLossPct;
        bValue = b.totalGainLossPct;
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt);
        bValue = new Date(b.createdAt);
        break;
      case 'updatedAt':
        aValue = new Date(a.updatedAt);
        bValue = new Date(b.updatedAt);
        break;
      default:
        aValue = a.name;
        bValue = b.name;
    }

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });
};

// Async thunks
export const fetchPortfolios = createAsyncThunk(
  'portfolios/fetchPortfolios',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🚀 Redux fetchPortfolios: Starting API call...');
      const response = await portfolioApi.getPortfolios();
      console.log('📡 Redux fetchPortfolios: API response received:', response);
      
      if (response.success !== false && response.data) {
        console.log('✅ Redux fetchPortfolios: Success, returning data:', response.data?.length, 'portfolios');
        return response.data;
      } else {
        console.error('❌ Redux fetchPortfolios: API returned success=false:', response.message);
        return rejectWithValue(response.message || 'Failed to fetch portfolios');
      }
    } catch (error) {
      console.error('❌ Redux fetchPortfolios: Exception caught:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch portfolios');
    }
  }
);

export const fetchPortfolioById = createAsyncThunk(
  'portfolios/fetchPortfolioById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await portfolioApi.getPortfolioById(id);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch portfolio');
    }
  }
);

export const createPortfolio = createAsyncThunk(
  'portfolios/createPortfolio',
  async (portfolioData: PortfolioCreateRequest, { rejectWithValue }) => {
    try {
      const response = await portfolioApi.createPortfolio(portfolioData);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create portfolio');
    }
  }
);

export const updatePortfolio = createAsyncThunk(
  'portfolios/updatePortfolio',
  async ({ id, data }: { id: string; data: PortfolioUpdateRequest }, { rejectWithValue }) => {
    try {
      const response = await portfolioApi.updatePortfolio(id, data);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update portfolio');
    }
  }
);

export const deletePortfolio = createAsyncThunk(
  'portfolios/deletePortfolio',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await portfolioApi.deletePortfolio(id);
      if (response.success) {
        return id;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete portfolio');
    }
  }
);

export const addHolding = createAsyncThunk(
  'portfolios/addHolding',
  async ({ portfolioId, holdingData }: { portfolioId: string; holdingData: AddHoldingRequest }, { rejectWithValue }) => {
    try {
      const response = await portfolioApi.addHolding(portfolioId, holdingData);
      if (response.success) {
        return response.data;
      } else {
        return rejectWithValue(response.message);
      }
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to add holding');
    }
  }
);

// Slice
const portfoliosSlice = createSlice({
  name: 'portfolios',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<Partial<PortfolioFilter>>) => {
      state.filter = { ...state.filter, ...action.payload };
      state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
    },
    resetFilter: (state) => {
      state.filter = initialFilter;
      state.filteredPortfolios = [...state.portfolios];
    },
    setCurrentPortfolio: (state, action: PayloadAction<Portfolio | null>) => {
      state.currentPortfolio = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updatePortfolioInList: (state, action: PayloadAction<Portfolio>) => {
      const index = state.portfolios.findIndex(p => p.portfolioId === action.payload.portfolioId);
      if (index !== -1) {
        state.portfolios[index] = action.payload;
        state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch portfolios
    builder
      .addCase(fetchPortfolios.pending, (state) => {
        console.log('🔄 Redux: fetchPortfolios.pending - Setting loading=true');
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPortfolios.fulfilled, (state, action) => {
        console.log('✅ Redux: fetchPortfolios.fulfilled - Received data:', action.payload?.length, 'portfolios');
        state.loading = false;
        state.portfolios = action.payload;
        state.filteredPortfolios = applyFilters(action.payload, state.filter);
        state.lastUpdated = new Date().toISOString();
        console.log('✅ Redux: State updated - portfolios:', state.portfolios.length, 'filteredPortfolios:', state.filteredPortfolios.length);
      })
      .addCase(fetchPortfolios.rejected, (state, action) => {
        console.log('❌ Redux: fetchPortfolios.rejected - Error:', action.payload);
        state.loading = false;
        state.error = action.payload as string;
      })

    // Fetch portfolio by ID
    builder
      .addCase(fetchPortfolioById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPortfolioById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentPortfolio = action.payload;
        // Also update in the list if it exists
        const index = state.portfolios.findIndex(p => p.portfolioId === action.payload.portfolioId);
        if (index !== -1) {
          state.portfolios[index] = action.payload;
          state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
        }
      })
      .addCase(fetchPortfolioById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

    // Create portfolio
    builder
      .addCase(createPortfolio.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPortfolio.fulfilled, (state, action) => {
        state.loading = false;
        state.portfolios.push(action.payload);
        state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
      })
      .addCase(createPortfolio.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

    // Update portfolio
    builder
      .addCase(updatePortfolio.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePortfolio.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.portfolios.findIndex(p => p.portfolioId === action.payload.portfolioId);
        if (index !== -1) {
          state.portfolios[index] = action.payload;
          state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
        }
        if (state.currentPortfolio?.portfolioId === action.payload.portfolioId) {
          state.currentPortfolio = action.payload;
        }
      })
      .addCase(updatePortfolio.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

    // Delete portfolio
    builder
      .addCase(deletePortfolio.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deletePortfolio.fulfilled, (state, action) => {
        state.loading = false;
        state.portfolios = state.portfolios.filter(p => p.portfolioId !== action.payload);
        state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
        if (state.currentPortfolio?.portfolioId === action.payload) {
          state.currentPortfolio = null;
        }
      })
      .addCase(deletePortfolio.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

    // Add holding
    builder
      .addCase(addHolding.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addHolding.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.portfolios.findIndex(p => p.portfolioId === action.payload.portfolioId);
        if (index !== -1) {
          state.portfolios[index] = action.payload;
          state.filteredPortfolios = applyFilters(state.portfolios, state.filter);
        }
        if (state.currentPortfolio?.portfolioId === action.payload.portfolioId) {
          state.currentPortfolio = action.payload;
        }
      })
      .addCase(addHolding.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setFilter,
  resetFilter,
  setCurrentPortfolio,
  clearError,
  updatePortfolioInList,
} = portfoliosSlice.actions;

export default portfoliosSlice.reducer;
