import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
}

// Function to get system preference
const getSystemPreference = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
};

// Function to get stored theme or default to light for SSR compatibility
const getInitialTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light'; // Default to light for SSR
  const stored = localStorage.getItem('theme') as ThemeMode;
  return stored || 'system';
};

// Function to determine if dark mode should be active
const getIsDark = (mode: ThemeMode): boolean => {
  if (typeof window === 'undefined') return false; // Default to light for SSR
  switch (mode) {
    case 'dark':
      return true;
    case 'light':
      return false;
    case 'system':
      return getSystemPreference();
    default:
      return false;
  }
};

// SSR-safe initial state
const initialState: ThemeState = {
  mode: 'light', // Always start with light for SSR
  isDark: false,
};

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    // Initialize theme from client-side storage
    initializeTheme: (state) => {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem('theme') as ThemeMode;
        const mode = stored || 'system';
        state.mode = mode;
        state.isDark = getIsDark(mode);
      }
    },
    setTheme: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
      state.isDark = getIsDark(action.payload);

      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', action.payload);
        console.log('Theme set to:', action.payload, 'isDark:', state.isDark);
      }
    },
    toggleTheme: (state) => {
      // If current mode is system, toggle based on current isDark state
      // Otherwise toggle between light and dark
      let newMode: ThemeMode;
      if (state.mode === 'system') {
        newMode = state.isDark ? 'light' : 'dark';
      } else {
        newMode = state.mode === 'light' ? 'dark' : 'light';
      }

      state.mode = newMode;
      state.isDark = getIsDark(newMode);

      // Persist to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', newMode);
        console.log('Theme toggled to:', newMode, 'isDark:', state.isDark);
      }
    },
    updateSystemPreference: (state) => {
      if (state.mode === 'system') {
        state.isDark = getSystemPreference();
      }
    },
  },
});

export const { initializeTheme, setTheme, toggleTheme, updateSystemPreference } = themeSlice.actions;
export default themeSlice.reducer;
