'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Language, KeyboardArrowDown } from '@mui/icons-material';
import { useLanguage } from '@/i18n/provider';
import { locales, localeNames, localeFlags, type Locale } from '@/i18n/config';

interface LanguageSwitcherProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showFlag?: boolean;
  showText?: boolean;
}

export default function LanguageSwitcher({ 
  className = '', 
  size = 'md',
  showFlag = true,
  showText = true
}: LanguageSwitcherProps) {
  const { locale, setLocale, t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close dropdown when pressing Escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, []);

  const handleLocaleChange = (newLocale: Locale) => {
    setLocale(newLocale);
    setIsOpen(false);
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className={`relative inline-block text-left ${className}`} ref={dropdownRef}>
      <button
        type="button"
        className={`
          inline-flex items-center justify-center space-x-2 
          border border-gray-300 dark:border-gray-600 
          rounded-md shadow-sm bg-white dark:bg-gray-800 
          text-gray-700 dark:text-gray-300 
          hover:bg-gray-50 dark:hover:bg-gray-700 
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 
          transition-colors duration-200
          ${sizeClasses[size]}
        `}
        onClick={() => setIsOpen(!isOpen)}
        aria-label={t('language.switchLanguage')}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {showFlag && (
          <span className="text-lg" aria-hidden="true">
            {localeFlags[locale]}
          </span>
        )}
        
        {!showFlag && !showText && (
          <Language className={iconSizeClasses[size]} />
        )}
        
        {showText && (
          <span className="font-medium">
            {localeNames[locale]}
          </span>
        )}
        
        <KeyboardArrowDown 
          className={`${iconSizeClasses[size]} transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="
          absolute right-0 z-50 mt-2 w-48 
          origin-top-right rounded-md 
          bg-white dark:bg-gray-800 
          shadow-lg ring-1 ring-black ring-opacity-5 
          border border-gray-200 dark:border-gray-700
          focus:outline-none
        ">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((loc) => (
              <button
                key={loc}
                type="button"
                className={`
                  group flex w-full items-center px-4 py-2 text-sm 
                  transition-colors duration-150
                  ${locale === loc 
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' 
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
                role="menuitem"
                onClick={() => handleLocaleChange(loc)}
              >
                <span className="text-lg mr-3" aria-hidden="true">
                  {localeFlags[loc]}
                </span>
                <span className="flex-1 text-left">
                  {localeNames[loc]}
                </span>
                {locale === loc && (
                  <span className="ml-2 text-blue-600 dark:text-blue-400" aria-hidden="true">
                    ✓
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 