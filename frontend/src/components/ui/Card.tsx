import React from 'react';
import { BaseComponentProps } from '@/types';

interface CardProps extends BaseComponentProps {
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'sm' | 'md' | 'lg';
  border?: boolean;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  className = '',
  children,
  padding = 'md',
  shadow = 'sm',
  border = true,
  hover = false,
}) => {
  const baseClasses = 'bg-white dark:bg-gray-800 rounded-lg';

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  const borderClass = border ? 'border border-gray-200 dark:border-gray-700' : '';
  const hoverClass = hover ? 'hover:shadow-md transition-shadow duration-200' : '';

  const classes = `${baseClasses} ${paddingClasses[padding]} ${shadowClasses[shadow]} ${borderClass} ${hoverClass} ${className}`.trim();

  return (
    <div className={classes}>
      {children}
    </div>
  );
};

// Sub-components for better semantic structure
const CardHeader: React.FC<BaseComponentProps> = ({ className = '', children }) => (
  <div className={`mb-4 ${className}`}>
    {children}
  </div>
);

const CardTitle: React.FC<BaseComponentProps> = ({ className = '', children }) => (
  <h3 className={`text-lg font-semibold text-gray-900 dark:text-gray-100 ${className}`}>
    {children}
  </h3>
);

const CardDescription: React.FC<BaseComponentProps> = ({ className = '', children }) => (
  <p className={`text-sm text-gray-600 dark:text-gray-400 ${className}`}>
    {children}
  </p>
);

const CardContent: React.FC<BaseComponentProps> = ({ className = '', children }) => (
  <div className={className}>
    {children}
  </div>
);

const CardFooter: React.FC<BaseComponentProps> = ({ className = '', children }) => (
  <div className={`mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 ${className}`}>
    {children}
  </div>
);

// Create compound component
const CardComponent = Object.assign(Card, {
  Header: CardHeader,
  Title: CardTitle,
  Description: CardDescription,
  Content: CardContent,
  Footer: CardFooter,
});

export default CardComponent;