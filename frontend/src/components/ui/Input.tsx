import { InputHTMLAttributes, forwardRef } from 'react'
import { BaseComponentProps } from '@/types'

interface InputProps extends InputHTMLAttributes<HTMLInputElement>, BaseComponentProps {
  label?: string
  error?: string
  helperText?: string
  variant?: 'default' | 'error' | 'success'
  fullWidth?: boolean
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className = '', 
    label,
    error,
    helperText,
    variant = 'default',
    fullWidth = false,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
    
    const baseClasses = 'px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 transition-colors bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100'
    
    const variantClasses = {
      default: 'border-gray-300 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500',
      error: 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500',
      success: 'border-green-300 dark:border-green-600 focus:ring-green-500 focus:border-green-500',
    }
    
    const widthClass = fullWidth ? 'w-full' : ''
    const finalVariant = error ? 'error' : variant
    
    const classes = `${baseClasses} ${variantClasses[finalVariant]} ${widthClass} ${className}`.trim()
    
    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label 
            htmlFor={inputId} 
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
          >
            {label}
          </label>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={classes}
          {...props}
        />
        
        {(error || helperText) && (
          <div className="mt-1">
            {error && (
              <div className="flex items-center text-sm text-red-600 dark:text-red-400">
                <svg 
                  className="w-4 h-4 mr-1" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
                {error}
              </div>
            )}
            {helperText && !error && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export default Input
