'use client';

import { useState } from 'react';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { deletePortfolio, setCurrentPortfolio } from '@/store/slices/portfoliosSlice';
import { Portfolio } from '@/types';
import Button from '@/components/ui/Button';
import PortfolioEditModal from './PortfolioEditModal';

export default function PortfolioListTable() {
  const dispatch = useAppDispatch();
  const { filteredPortfolios, loading } = useAppSelector(state => state.portfolios);
  const [editingPortfolio, setEditingPortfolio] = useState<Portfolio | null>(null);
  const [deletingPortfolio, setDeletingPortfolio] = useState<string | null>(null);

  const handleEdit = (portfolio: Portfolio) => {
    setEditingPortfolio(portfolio);
  };

  const handleDelete = async (portfolioId: string) => {
    if (window.confirm('Are you sure you want to delete this portfolio? This action cannot be undone.')) {
      setDeletingPortfolio(portfolioId);
      try {
        await dispatch(deletePortfolio(portfolioId)).unwrap();
      } catch (error) {
        console.error('Failed to delete portfolio:', error);
      } finally {
        setDeletingPortfolio(null);
      }
    }
  };

  const handleView = (portfolio: Portfolio) => {
    dispatch(setCurrentPortfolio(portfolio));
    // Navigate to portfolio detail page
    window.location.href = `/portfolios/${portfolio.portfolioId}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
      closed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      liquidating: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };

    return (
      <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${statusColors[status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getRiskLevelBadge = (riskLevel?: string) => {
    if (!riskLevel) return null;

    const riskColors = {
      very_low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      moderate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      very_high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };

    const riskLabels = {
      very_low: 'Low',
      low: 'Low',
      moderate: 'Med',
      high: 'High',
      very_high: 'High',
    };

    return (
      <span className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${riskColors[riskLevel as keyof typeof riskColors] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'}`}>
        {riskLabels[riskLevel as keyof typeof riskLabels] || riskLevel}
      </span>
    );
  };

  if (loading && filteredPortfolios.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">Loading portfolios...</span>
      </div>
    );
  }

  if (filteredPortfolios.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 dark:text-gray-400 text-lg mb-2">No portfolios found</div>
        <p className="text-gray-400 dark:text-gray-500">Create your first portfolio to get started</p>
      </div>
    );
  }

  return (
    <>
      <div className="overflow-x-auto">
        <div className="inline-block min-w-full align-middle">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed lg:table-auto">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4 lg:w-auto">
                  Portfolio
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/6 lg:w-auto">
                  Type
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/6 lg:w-auto">
                  Value
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/6 lg:w-auto">
                  P&L
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider text-center w-16 lg:w-auto">
                  Holdings
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16 lg:w-auto">
                  Risk
                </th>
                <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4 lg:w-auto">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredPortfolios.map((portfolio) => (
                <tr key={portfolio.portfolioId} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100 max-w-40 truncate" title={portfolio.name}>
                        {portfolio.name}
                      </div>
                      <div className="text-xs text-gray-400 dark:text-gray-500">
                        {new Date(portfolio.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <div className="text-sm text-gray-900 dark:text-gray-100 capitalize">
                        {portfolio.portfolioType.replace('_', ' ')}
                      </div>
                      {getStatusBadge(portfolio.status)}
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {formatCurrency(portfolio.totalValue)}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatCurrency(portfolio.cashBalance)}
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div>
                      <div className={`text-sm font-medium ${portfolio.totalGainLoss >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {formatCurrency(portfolio.totalGainLoss)}
                      </div>
                      <div className={`text-xs ${portfolio.totalGainLossPct >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {formatPercentage(portfolio.totalGainLossPct)}
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100 text-center">
                      {portfolio.holdingsCount || portfolio.holdings.length}
                    </div>
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap">
                    {getRiskLevelBadge(portfolio.riskLevel)}
                  </td>
                  <td className="px-3 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleView(portfolio)}
                        className="text-xs px-2 py-1"
                      >
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(portfolio)}
                        className="text-xs px-2 py-1"
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(portfolio.portfolioId)}
                        loading={deletingPortfolio === portfolio.portfolioId}
                        disabled={deletingPortfolio === portfolio.portfolioId}
                        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 border-red-300 hover:border-red-400 dark:border-red-600 dark:hover:border-red-500 text-xs px-2 py-1"
                      >
                        Del
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Edit Modal */}
      {editingPortfolio && (
        <PortfolioEditModal
          portfolio={editingPortfolio}
          isOpen={!!editingPortfolio}
          onClose={() => setEditingPortfolio(null)}
          onSuccess={() => setEditingPortfolio(null)}
        />
      )}
    </>
  );
}
