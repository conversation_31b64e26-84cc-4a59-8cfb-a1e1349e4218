'use client';

import { useState, useEffect } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { updatePortfolio } from '@/store/slices/portfoliosSlice';
import { Portfolio, PortfolioUpdateRequest, PortfolioType, PortfolioStatus } from '@/types';
import Button from '@/components/ui/Button';

interface PortfolioEditModalProps {
  portfolio: Portfolio;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function PortfolioEditModal({ portfolio, isOpen, onClose, onSuccess }: PortfolioEditModalProps) {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<PortfolioUpdateRequest>({
    name: portfolio.name,
    description: portfolio.description,
    portfolioType: portfolio.portfolioType,
    status: portfolio.status,
    baseCurrency: portfolio.baseCurrency,
    cashBalance: portfolio.cashBalance,
    riskLevel: portfolio.riskLevel,
    benchmark: portfolio.benchmark,
    tags: portfolio.tags,
    customFields: portfolio.customFields,
  });

  useEffect(() => {
    setFormData({
      name: portfolio.name,
      description: portfolio.description,
      portfolioType: portfolio.portfolioType,
      status: portfolio.status,
      baseCurrency: portfolio.baseCurrency,
      cashBalance: portfolio.cashBalance,
      riskLevel: portfolio.riskLevel,
      benchmark: portfolio.benchmark,
      tags: portfolio.tags,
      customFields: portfolio.customFields,
    });
  }, [portfolio]);

  const portfolioTypes: { value: PortfolioType; label: string }[] = [
    { value: 'personal', label: 'Personal' },
    { value: 'retirement', label: 'Retirement' },
    { value: 'taxable', label: 'Taxable' },
    { value: 'trust', label: 'Trust' },
    { value: 'corporate', label: 'Corporate' },
    { value: 'education', label: 'Education' },
  ];

  const portfolioStatuses: { value: PortfolioStatus; label: string }[] = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'closed', label: 'Closed' },
    { value: 'liquidating', label: 'Liquidating' },
  ];

  const riskLevels = [
    { value: 'very_low', label: 'Very Low' },
    { value: 'low', label: 'Low' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'high', label: 'High' },
    { value: 'very_high', label: 'Very High' },
  ];

  const benchmarks = [
    { value: '', label: 'No Benchmark' },
    { value: 'S&P 500', label: 'S&P 500' },
    { value: 'NASDAQ', label: 'NASDAQ' },
    { value: 'Russell 2000', label: 'Russell 2000' },
    { value: 'MSCI World', label: 'MSCI World' },
    { value: 'Bloomberg Aggregate Bond', label: 'Bloomberg Aggregate Bond' },
  ];

  const handleInputChange = (field: keyof PortfolioUpdateRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTagsChange = (value: string) => {
    const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    handleInputChange('tags', tags);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.name?.trim()) {
        throw new Error('Portfolio name is required');
      }

      await dispatch(updatePortfolio({ 
        id: portfolio.portfolioId, 
        data: formData 
      })).unwrap();
      onSuccess();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update portfolio');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Edit Portfolio</h2>
          <p className="text-sm text-gray-500 mt-1">Portfolio ID: {portfolio.portfolioId}</p>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="text-red-800 text-sm">{error}</div>
            </div>
          )}

          {/* Portfolio Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Portfolio Name *
            </label>
            <input
              type="text"
              required
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter portfolio name"
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter portfolio description"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Portfolio Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Portfolio Type
              </label>
              <select
                value={formData.portfolioType || portfolio.portfolioType}
                onChange={(e) => handleInputChange('portfolioType', e.target.value as PortfolioType)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {portfolioTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status || portfolio.status}
                onChange={(e) => handleInputChange('status', e.target.value as PortfolioStatus)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {portfolioStatuses.map((status) => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Risk Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Risk Level
              </label>
              <select
                value={formData.riskLevel || portfolio.riskLevel || ''}
                onChange={(e) => handleInputChange('riskLevel', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">No Risk Level</option>
                {riskLevels.map((level) => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Cash Balance */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Cash Balance
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={formData.cashBalance ?? portfolio.cashBalance}
                onChange={(e) => handleInputChange('cashBalance', Number(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Benchmark */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Benchmark
            </label>
            <select
              value={formData.benchmark ?? portfolio.benchmark ?? ''}
              onChange={(e) => handleInputChange('benchmark', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {benchmarks.map((benchmark) => (
                <option key={benchmark.value} value={benchmark.value}>
                  {benchmark.label}
                </option>
              ))}
            </select>
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>
            <input
              type="text"
              value={(formData.tags ?? portfolio.tags)?.join(', ') || ''}
              onChange={(e) => handleTagsChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter tags separated by commas"
            />
            <p className="text-xs text-gray-500 mt-1">
              Separate multiple tags with commas (e.g., growth, tech, long-term)
            </p>
          </div>
        </form>

        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            loading={loading}
            disabled={loading}
          >
            Update Portfolio
          </Button>
        </div>
      </div>
    </div>
  );
}
