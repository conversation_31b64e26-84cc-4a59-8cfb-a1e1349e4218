'use client';

import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setFilter, resetFilter } from '@/store/slices/portfoliosSlice';
import { PortfolioType, PortfolioStatus } from '@/types';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

export default function PortfolioFilters() {
  const dispatch = useAppDispatch();
  const { filter } = useAppSelector(state => state.portfolios);

  const handleFilterChange = (key: string, value: any) => {
    dispatch(setFilter({ [key]: value }));
  };

  const handleReset = () => {
    dispatch(resetFilter());
  };

  const portfolioTypes: { value: PortfolioType | ''; label: string }[] = [
    { value: '', label: 'All Types' },
    { value: 'personal', label: 'Personal' },
    { value: 'retirement', label: 'Retirement' },
    { value: 'taxable', label: 'Taxable' },
    { value: 'trust', label: 'Trust' },
    { value: 'corporate', label: 'Corporate' },
    { value: 'education', label: 'Education' },
  ];

  const portfolioStatuses: { value: PortfolioStatus | ''; label: string }[] = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'closed', label: 'Closed' },
    { value: 'liquidating', label: 'Liquidating' },
  ];

  const riskLevels: { value: string; label: string }[] = [
    { value: '', label: 'All Risk Levels' },
    { value: 'very_low', label: 'Very Low' },
    { value: 'low', label: 'Low' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'high', label: 'High' },
    { value: 'very_high', label: 'Very High' },
  ];

  const sortOptions = [
    { value: 'name', label: 'Name' },
    { value: 'totalValue', label: 'Total Value' },
    { value: 'totalGainLossPct', label: 'Performance' },
    { value: 'createdAt', label: 'Created Date' },
    { value: 'updatedAt', label: 'Last Updated' },
  ];

  return (
    <Card>
      <Card.Header>
        <Card.Title>Filters & Search</Card.Title>
      </Card.Header>
      <Card.Content>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              placeholder="Search portfolios..."
              value={filter.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Portfolio Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Portfolio Type
            </label>
            <select
              value={filter.portfolioType}
              onChange={(e) => handleFilterChange('portfolioType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {portfolioTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filter.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {portfolioStatuses.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
          </div>

          {/* Risk Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Risk Level
            </label>
            <select
              value={filter.riskLevel || ''}
              onChange={(e) => handleFilterChange('riskLevel', e.target.value || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {riskLevels.map((level) => (
                <option key={level.value} value={level.value}>
                  {level.label}
                </option>
              ))}
            </select>
          </div>

          {/* Value Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Min Value ($)
            </label>
            <input
              type="number"
              placeholder="0"
              value={filter.minValue || ''}
              onChange={(e) => handleFilterChange('minValue', e.target.value ? Number(e.target.value) : null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Value ($)
            </label>
            <input
              type="number"
              placeholder="No limit"
              value={filter.maxValue || ''}
              onChange={(e) => handleFilterChange('maxValue', e.target.value ? Number(e.target.value) : null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort By
            </label>
            <select
              value={filter.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Sort Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sort Order
            </label>
            <select
              value={filter.sortOrder}
              onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>

        {/* Reset Button */}
        <div className="mt-4 flex justify-end">
          <Button variant="outline" onClick={handleReset}>
            Reset Filters
          </Button>
        </div>
      </Card.Content>
    </Card>
  );
}
