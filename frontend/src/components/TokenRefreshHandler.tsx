'use client';

import { useSession, signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';
import Button from '@/components/ui/Button';

export default function TokenRefreshHandler() {
  const { data: session, status } = useSession();
  const [showRefreshPrompt, setShowRefreshPrompt] = useState(false);

  useEffect(() => {
    if (session?.expiresAt) {
      const expiresAt = session.expiresAt * 1000; // Convert to milliseconds
      const now = Date.now();
      const timeUntilExpiry = expiresAt - now;
      
      // Show refresh prompt 5 minutes before expiry
      const fiveMinutes = 5 * 60 * 1000;
      
      if (timeUntilExpiry <= fiveMinutes && timeUntilExpiry > 0) {
        setShowRefreshPrompt(true);
      } else if (timeUntilExpiry <= 0) {
        // Token has already expired
        setShowRefreshPrompt(true);
      } else {
        // Set a timer to show the prompt 5 minutes before expiry
        const timer = setTimeout(() => {
          setShowRefreshPrompt(true);
        }, timeUntilExpiry - fiveMinutes);
        
        return () => clearTimeout(timer);
      }
    }
  }, [session]);

  const handleRefresh = () => {
    // Force re-authentication
    signIn('cognito', { callbackUrl: window.location.href });
  };

  const handleDismiss = () => {
    setShowRefreshPrompt(false);
  };

  if (status === 'loading' || !session || !showRefreshPrompt) {
    return null;
  }

  const isExpired = session.expiresAt && Date.now() >= session.expiresAt * 1000;

  return (
    <div className="fixed top-4 right-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-lg max-w-sm">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            {isExpired ? 'Session Expired' : 'Session Expiring Soon'}
          </h3>
          <p className="mt-1 text-sm text-yellow-700">
            {isExpired 
              ? 'Your session has expired. Please sign in again to continue.'
              : 'Your session will expire soon. Please refresh to continue using the application.'
            }
          </p>
          <div className="mt-3 flex space-x-2">
            <Button
              onClick={handleRefresh}
              size="sm"
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              {isExpired ? 'Sign In Again' : 'Refresh Session'}
            </Button>
            {!isExpired && (
              <Button
                onClick={handleDismiss}
                size="sm"
                variant="outline"
                className="border-yellow-300 text-yellow-700 hover:bg-yellow-50"
              >
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
