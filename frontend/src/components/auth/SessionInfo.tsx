'use client'

import { useSession } from 'next-auth/react'
import { BaseComponentProps } from '@/types'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import MFAStatus from './MFAStatus'
import useAuth from '@/hooks/useAuth'
import { performCompleteSignOut } from '@/lib/auth-utils'

interface SessionInfoProps extends BaseComponentProps {
  showActions?: boolean
  compact?: boolean
}

const SessionInfo: React.FC<SessionInfoProps> = ({ 
  className = '',
  showActions = true,
  compact = false
}) => {
  const { data: session, status } = useSession()
  const { 
    isSessionExpiring, 
    isSessionExpired, 
    authTime, 
    expiresAt 
  } = useAuth()

  if (status === 'loading') {
    return (
      <Card className={`animate-pulse ${className}`}>
        <Card.Content>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </Card.Content>
      </Card>
    )
  }

  if (!session) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Not Signed In</Card.Title>
        </Card.Header>
        <Card.Content>
          <p className="text-sm text-gray-600 mb-4">
            You are not currently signed in.
          </p>
          {showActions && (
            <Button variant="primary" onClick={() => window.location.href = '/auth/signin'}>
              Sign In
            </Button>
          )}
        </Card.Content>
      </Card>
    )
  }

  const formatDate = (date: Date | null) => {
    if (!date) return 'Unknown'
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date)
  }

  if (compact) {
    return (
      <Card className={className}>
        <Card.Content>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {session.user?.image && (
                <img 
                  src={session.user.image} 
                  alt={session.user.name || 'User'} 
                  className="w-10 h-10 rounded-full"
                />
              )}
              <div>
                <p className="font-medium text-gray-900">
                  {session.user?.name || session.user?.email}
                </p>
                <p className="text-sm text-gray-500">
                  {session.user?.email}
                </p>
              </div>
            </div>
            
            {(isSessionExpiring || isSessionExpired) && (
              <div className="text-right">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  isSessionExpired 
                    ? 'bg-red-100 text-red-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {isSessionExpired ? 'Expired' : 'Expiring Soon'}
                </span>
              </div>
            )}
          </div>
        </Card.Content>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <Card.Header>
        <Card.Title>Session Information</Card.Title>
        <Card.Description>
          Your current authentication session details
        </Card.Description>
      </Card.Header>
      
      <Card.Content>
        <div className="space-y-4">
          {/* User Information */}
          <div className="flex items-center space-x-4">
            {session.user?.image && (
              <img 
                src={session.user.image} 
                alt={session.user.name || 'User'} 
                className="w-12 h-12 rounded-full"
              />
            )}
            <div>
              <p className="font-medium text-gray-900">
                {session.user?.name || 'Anonymous User'}
              </p>
              <p className="text-sm text-gray-600">
                {session.user?.email || 'No email provided'}
              </p>
            </div>
          </div>

          {/* Session Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200">
            <div>
              <p className="text-sm font-medium text-gray-700">Authentication Time</p>
              <p className="text-sm text-gray-600">{formatDate(authTime)}</p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-700">Session Expires</p>
              <p className={`text-sm ${
                isSessionExpired 
                  ? 'text-red-600' 
                  : isSessionExpiring 
                    ? 'text-yellow-600' 
                    : 'text-gray-600'
              }`}>
                {formatDate(expiresAt ?? null)}
              </p>
            </div>
          </div>

          {/* Session Status Warnings */}
          {(isSessionExpiring || isSessionExpired) && (
            <div className={`p-3 rounded-md ${
              isSessionExpired 
                ? 'bg-red-50 border border-red-200' 
                : 'bg-yellow-50 border border-yellow-200'
            }`}>
              <p className={`text-sm ${
                isSessionExpired ? 'text-red-800' : 'text-yellow-800'
              }`}>
                {isSessionExpired 
                  ? 'Your session has expired. Please sign in again.' 
                  : 'Your session will expire soon. Consider refreshing your session.'
                }
              </p>
            </div>
          )}

          {/* MFA Status */}
          <MFAStatus showDetails={false} />
        </div>
      </Card.Content>

      {showActions && (
        <Card.Footer>
          <div className="flex space-x-3">
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
              size="sm"
            >
              Refresh Session
            </Button>
            <Button
              variant="destructive"
              onClick={() => performCompleteSignOut('/')}
              size="sm"
            >
              Sign Out
            </Button>
          </div>
        </Card.Footer>
      )}
    </Card>
  )
}

export default SessionInfo 