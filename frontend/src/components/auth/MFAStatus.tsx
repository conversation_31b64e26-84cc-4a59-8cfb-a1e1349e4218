'use client'

import { useSession } from 'next-auth/react'
import { BaseComponentProps } from '@/types'
import Card from '@/components/ui/Card'

interface MFAStatusProps extends BaseComponentProps {
  showDetails?: boolean
}

const MFAStatus: React.FC<MFAStatusProps> = ({ 
  className = '',
  showDetails = true 
}) => {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const mfaEnabled = session.mfaEnabled
  const authTime = session.authTime

  if (!showDetails) {
    return (
      <div className={`flex items-center space-x-2 ${className}`} role="status" aria-label="Multi-factor authentication status">
        <div 
          className={`w-2 h-2 rounded-full ${mfaEnabled ? 'bg-green-500' : 'bg-gray-400'}`}
          aria-hidden="true"
        />
        <span className="text-sm text-gray-600 dark:text-gray-400">
          MFA {mfaEnabled ? 'Enabled' : 'Not Available'}
        </span>
      </div>
    )
  }

  return (
    <Card className={className}>
      <Card.Header>
        <Card.Title className="flex items-center space-x-2">
          <div 
            className={`w-3 h-3 rounded-full ${mfaEnabled ? 'bg-green-500' : 'bg-gray-400'}`}
            aria-hidden="true"
          />
          <span>Multi-Factor Authentication</span>
        </Card.Title>
      </Card.Header>
      
      <Card.Content>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Status:</span>
            <span className={`text-sm px-2 py-1 rounded-full ${
              mfaEnabled 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-800'
            }`}>
              {mfaEnabled ? 'Active' : 'Not Available'}
            </span>
          </div>

          {authTime && (
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">Last Auth:</span>
              <span className="text-sm text-gray-600">
                {new Date(authTime * 1000).toLocaleString()}
              </span>
            </div>
          )}

          <div className="pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-500">
              {mfaEnabled 
                ? 'Your account is protected with multi-factor authentication.'
                : 'MFA configuration is managed by your administrator.'
              }
            </p>
          </div>
        </div>
      </Card.Content>
    </Card>
  )
}

export default MFAStatus 