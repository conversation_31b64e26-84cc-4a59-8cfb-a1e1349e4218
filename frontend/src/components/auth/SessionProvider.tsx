'use client'

import { Session<PERSON>rovider as NextAuthSessionProvider } from 'next-auth/react'
import { Session } from 'next-auth'
import { BaseComponentProps } from '@/types'

interface SessionProviderProps extends BaseComponentProps {
  session?: Session | null
}

const SessionProvider: React.FC<SessionProviderProps> = ({ 
  children, 
  session 
}) => {
  return (
    <NextAuthSessionProvider session={session}>
      {children}
    </NextAuthSessionProvider>
  )
}

export default SessionProvider 