'use client'

import { signIn, useSession } from 'next-auth/react'
import Button from '@/components/ui/Button'
import { BaseComponentProps } from '@/types'
import MFAStatus from './MFAStatus'
import { performCompleteSignOut, performFreshSignIn } from '@/lib/auth-utils'

interface AuthButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  showUserInfo?: boolean
  redirectTo?: string
}

const AuthButton: React.FC<AuthButtonProps> = ({ 
  className = '',
  variant = 'primary',
  size = 'md',
  showUserInfo = false,
  redirectTo = '/dashboard'
}) => {
  const { data: session, status } = useSession()

  if (status === 'loading') {
    return (
      <Button 
        variant={variant} 
        size={size} 
        className={className}
        loading
        disabled
        aria-label="Loading authentication status"
      >
        Loading...
      </Button>
    )
  }

  if (session) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        {showUserInfo && session.user && (
          <div className="flex items-center gap-3 text-sm text-gray-700 dark:text-gray-300">
            <div className="flex items-center gap-2">
              {session.user.image && (
                <img
                  src={session.user.image}
                  alt={`Profile picture of ${session.user.name || session.user.email || 'User'}`}
                  className="w-8 h-8 rounded-full"
                />
              )}
              <span className="hidden sm:inline">
                {session.user.name || session.user.email}
              </span>
            </div>
            <MFAStatus showDetails={false} className="hidden lg:flex" />
          </div>
        )}
        <Button
          variant={variant}
          size={size}
          onClick={() => performCompleteSignOut('/')}
          aria-label={`Sign out ${session.user?.name || session.user?.email || 'current user'}`}
        >
          Sign Out
        </Button>
      </div>
    )
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={() => performFreshSignIn('cognito', redirectTo)}
      aria-label="Sign in to access your account"
    >
      Sign In
    </Button>
  )
}

export default AuthButton 