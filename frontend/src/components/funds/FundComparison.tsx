'use client';

import { Fund } from '@/types';
import { Card } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { formatCurrency, formatDate } from '@/utils';
import { Close } from '@mui/icons-material';

interface FundComparisonProps {
  funds: Fund[];
  onClose: () => void;
}

export default function FundComparison({ funds, onClose }: FundComparisonProps) {
  const { t } = useTranslation();

  if (funds.length === 0) {
    return null;
  }

  const comparisonData = [
    {
      category: t('funds.basicInfo'),
      rows: [
        {
          label: t('funds.fundName'),
          getValue: (fund: Fund) => fund.name,
        },
        {
          label: t('funds.type'),
          getValue: (fund: Fund) => fund.type,
        },
        {
          label: t('funds.category'),
          getValue: (fund: Fund) => fund.category,
        },
        {
          label: t('funds.fundManager'),
          getValue: (fund: Fund) => fund.fundManager,
        },
        {
          label: t('funds.inceptionDate'),
          getValue: (fund: Fund) => formatDate(fund.inceptionDate),
        },
        {
          label: t('funds.riskLevel'),
          getValue: (fund: Fund) => fund.riskLevel,
        },
      ],
    },
    {
      category: t('funds.performance'),
      rows: [
        {
          label: t('funds.nav'),
          getValue: (fund: Fund) => formatCurrency(fund.nav, 'USD'),
        },
        {
          label: t('funds.change'),
          getValue: (fund: Fund) => (
            <span className={fund.change >= 0 ? 'text-green-600' : 'text-red-600'}>
              {fund.change >= 0 ? '+' : ''}{formatCurrency(fund.change, 'USD')} ({fund.changePercent.toFixed(2)}%)
            </span>
          ),
        },
        {
          label: t('funds.oneYearReturn'),
          getValue: (fund: Fund) => `${fund.performance.oneYear.toFixed(2)}%`,
        },
        {
          label: t('funds.threeYearReturn'),
          getValue: (fund: Fund) => `${fund.performance.threeYears.toFixed(2)}%`,
        },
        {
          label: t('funds.fiveYearReturn'),
          getValue: (fund: Fund) => `${fund.performance.fiveYears.toFixed(2)}%`,
        },
      ],
    },
    {
      category: t('funds.fees'),
      rows: [
        {
          label: t('funds.expenseRatio'),
          getValue: (fund: Fund) => `${fund.expenseRatio.toFixed(2)}%`,
        },
        {
          label: t('funds.minimumInvestment'),
          getValue: (fund: Fund) => formatCurrency(fund.minimumInvestment, 'USD'),
        },
      ],
    },
    {
      category: t('funds.marketData'),
      rows: [
        {
          label: t('funds.aum'),
          getValue: (fund: Fund) => `$${(fund.aum / 1000000).toFixed(1)}M`,
        },
        {
          label: t('funds.rating'),
          getValue: (fund: Fund) => `${fund.rating}/5 ⭐`,
        },
        {
          label: 'Volume',
          getValue: (fund: Fund) => fund.volume.toLocaleString(),
        },
      ],
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t('funds.fundComparison')}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('funds.comparisonTable')} ({funds.length} funds)
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            aria-label={t('funds.closeComparison')}
          >
            <Close className="w-6 h-6 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Comparison Table */}
        <div className="overflow-auto max-h-[calc(90vh-120px)]">
          <div className="p-6">
            {comparisonData.map((section, sectionIndex) => (
              <Card key={sectionIndex} className="mb-6">
                <Card.Header>
                  <Card.Title>{section.category}</Card.Title>
                </Card.Header>
                <Card.Content className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="py-3 px-4 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4">
                            Metric
                          </th>
                          {funds.map((fund) => (
                            <th
                              key={fund.id}
                              className="py-3 px-4 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                            >
                              <div className="truncate max-w-xs" title={fund.name}>
                                {fund.name}
                              </div>
                              <div className="text-xs text-gray-400 font-normal normal-case">
                                {fund.symbol}
                              </div>
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {section.rows.map((row, rowIndex) => (
                          <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="py-3 px-4 text-sm font-medium text-gray-900 dark:text-gray-100">
                              {row.label}
                            </td>
                            {funds.map((fund) => (
                              <td
                                key={fund.id}
                                className="py-3 px-4 text-sm text-gray-600 dark:text-gray-300"
                              >
                                {typeof row.getValue(fund) === 'string' || typeof row.getValue(fund) === 'number'
                                  ? row.getValue(fund)
                                  : row.getValue(fund)
                                }
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </Card.Content>
              </Card>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t('funds.closeComparison')}
          </button>
        </div>
      </div>
    </div>
  );
}
