import React from 'react';
import { Card } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { Article, AccessTime } from '@mui/icons-material';

interface NewsItem {
  id: string;
  title: string;
  summary: string;
  source: string;
  publishedAt: string;
  link?: string;
}

interface NewsFeedProps {
  fundId: string;
  fundName: string;
}

const NewsFeed: React.FC<NewsFeedProps> = ({ fundId, fundName }) => {
  const { t } = useTranslation();
  
  // Mock news data - in real implementation, this would come from an API
  const newsItems: NewsItem[] = [
    {
      id: '1',
      title: `${fundName} Outperforms Benchmark in Q4`,
      summary: 'The fund has delivered strong returns beating its benchmark index by 2.3% in the fourth quarter...',
      source: 'Financial Times',
      publishedAt: '2 hours ago',
      link: '#'
    },
    {
      id: '2',
      title: 'Fund Manager Commentary on Market Outlook',
      summary: 'The fund manager shares insights on the current market conditions and investment strategy going forward...',
      source: 'Reuters',
      publishedAt: '5 hours ago',
      link: '#'
    },
    {
      id: '3',
      title: 'Portfolio Rebalancing Announcement',
      summary: 'The fund announces quarterly portfolio rebalancing with increased allocation to technology sector...',
      source: 'Bloomberg',
      publishedAt: '1 day ago',
      link: '#'
    }
  ];

  return (
    <div className="space-y-4">
      {newsItems.length === 0 ? (
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <Article className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600 dark:text-gray-400">{t('funds.noNewsAvailable')}</p>
            </div>
          </Card.Content>
        </Card>
      ) : (
        newsItems.map((item) => (
          <Card key={item.id} className="hover:shadow-md transition-shadow">
            <Card.Content>
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-3">
                    {item.summary}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">{item.source}</span>
                    <div className="flex items-center gap-1">
                      <AccessTime className="w-4 h-4" />
                      <span>{item.publishedAt}</span>
                    </div>
                  </div>
                </div>
                {item.link && (
                  <a
                    href={item.link}
                    className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {t('common.readMore')}
                  </a>
                )}
              </div>
            </Card.Content>
          </Card>
        ))
      )}
    </div>
  );
};

export default NewsFeed;