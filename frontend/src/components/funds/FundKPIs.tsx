import { FundDetails } from '@/types';
import { Card } from '@/components/ui';
import {
  TrendingUp,
  TrendingDown,
  ShowChart,
  Assessment,
  Security,
  Speed,
  Timeline,
  Analytics
} from '@mui/icons-material';

interface FundKPIsProps {
  kpis: FundDetails['analytics']['kpis'];
  riskMetrics: FundDetails['analytics']['riskMetrics'];
}

export default function FundKPIs({ kpis, riskMetrics }: FundKPIsProps) {
  const kpiItems = [
    {
      label: 'Total Return',
      value: kpis.totalReturn,
      format: 'percentage',
      description: 'Cumulative return since inception',
      icon: kpis.totalReturn >= 0 ? TrendingUp : TrendingDown,
      iconColor: kpis.totalReturn >= 0 ? 'text-green-600' : 'text-red-600',
    },
    {
      label: 'Annualized Return',
      value: kpis.annualizedReturn,
      format: 'percentage',
      description: 'Average yearly return',
      icon: kpis.annualizedReturn >= 0 ? TrendingUp : TrendingDown,
      iconColor: kpis.annualizedReturn >= 0 ? 'text-green-600' : 'text-red-600',
    },
    {
      label: 'Volatility',
      value: kpis.volatility,
      format: 'percentage',
      description: 'Standard deviation of returns',
      icon: ShowChart,
      iconColor: 'text-blue-600',
    },
    {
      label: 'Sharpe Ratio',
      value: kpis.sharpeRatio,
      format: 'number',
      description: 'Risk-adjusted return measure',
      icon: Assessment,
      iconColor: 'text-purple-600',
    },
    {
      label: 'Alpha',
      value: kpis.alpha,
      format: 'percentage',
      description: 'Excess return vs benchmark',
      icon: kpis.alpha >= 0 ? TrendingUp : TrendingDown,
      iconColor: kpis.alpha >= 0 ? 'text-green-600' : 'text-red-600',
    },
    {
      label: 'Beta',
      value: kpis.beta,
      format: 'number',
      description: 'Market sensitivity measure',
      icon: Timeline,
      iconColor: 'text-indigo-600',
    },
    {
      label: 'Max Drawdown',
      value: kpis.maxDrawdown,
      format: 'percentage',
      description: 'Largest peak-to-trough decline',
      icon: TrendingDown,
      iconColor: 'text-red-600',
    },
    {
      label: 'Information Ratio',
      value: kpis.informationRatio,
      format: 'number',
      description: 'Active return per unit of risk',
      icon: Analytics,
      iconColor: 'text-teal-600',
    },
  ];

  const riskItems = [
    {
      label: 'Standard Deviation',
      value: riskMetrics.standardDeviation,
      format: 'percentage',
      description: 'Volatility measure',
      icon: ShowChart,
      iconColor: 'text-orange-600',
    },
    {
      label: 'Downside Risk',
      value: riskMetrics.downSideRisk,
      format: 'percentage',
      description: 'Risk of negative returns',
      icon: TrendingDown,
      iconColor: 'text-red-600',
    },
    {
      label: 'Value at Risk',
      value: riskMetrics.varRisk,
      format: 'percentage',
      description: 'Potential loss with 95% confidence',
      icon: Security,
      iconColor: 'text-red-600',
    },
    {
      label: 'Sortino Ratio',
      value: riskMetrics.sortRatio,
      format: 'number',
      description: 'Downside risk-adjusted return',
      icon: Assessment,
      iconColor: 'text-blue-600',
    },
    {
      label: 'Calmar Ratio',
      value: riskMetrics.calmarRatio,
      format: 'number',
      description: 'Return vs max drawdown',
      icon: Speed,
      iconColor: 'text-green-600',
    },
  ];

  const formatValue = (value: number, format: string) => {
    if (format === 'percentage') {
      return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
    }
    return value.toFixed(2);
  };

  const getValueColor = (value: number, format: string) => {
    if (format === 'percentage') {
      return value >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';
    }
    return 'text-gray-900 dark:text-gray-100';
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Key Performance Indicators */}
      <Card>
        <Card.Header>
          <Card.Title>Key Performance Indicators</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {kpiItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <div key={item.label} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <IconComponent className={`w-5 h-5 ${item.iconColor} dark:${item.iconColor.replace('text-', 'text-')}`} />
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</div>
                    </div>
                    <div className={`text-lg font-bold ${getValueColor(item.value, item.format)}`}>
                      {formatValue(item.value, item.format)}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{item.description}</div>
                </div>
              );
            })}
          </div>
        </Card.Content>
      </Card>

      {/* Risk Metrics */}
      <Card>
        <Card.Header>
          <Card.Title>Risk Metrics</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 gap-4">
            {riskItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <div key={item.label} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <IconComponent className={`w-5 h-5 ${item.iconColor} dark:${item.iconColor.replace('text-', 'text-')}`} />
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</div>
                    </div>
                    <div className={`text-lg font-bold ${getValueColor(item.value, item.format)}`}>
                      {formatValue(item.value, item.format)}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{item.description}</div>
                </div>
              );
            })}
          </div>
        </Card.Content>
      </Card>
    </div>
  );
} 