'use client';

import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setSortBy, toggleColumn } from '@/store/slices/fundsSlice';
import { formatCurrency, formatDate } from '@/utils';
import { Fund } from '@/types';
import Button from '@/components/ui/Button';
import Link from 'next/link';
import { useTranslation } from '@/i18n/provider';

interface FundListTableProps {
  selectedFunds?: string[];
  onFundSelection?: (fundId: string, isSelected: boolean) => void;
  maxSelection?: number;
}

interface ColumnConfig {
  key: keyof Fund | 'actions' | 'select';
  label: string;
  sortable: boolean;
  format?: (value: any, fund: Fund, props?: any) => React.ReactNode;
  className?: string;
}

const COLUMN_CONFIGS: ColumnConfig[] = [
  {
    key: 'select',
    label: '',
    sortable: false,
    format: (_, fund: Fund, props: any) => {
      const { selectedFunds, onFundSelection, maxSelection, t } = props;
      const isSelected = selectedFunds.includes(fund.id);
      const canSelect = !isSelected && selectedFunds.length < maxSelection;

      return onFundSelection ? (
        <input
          type="checkbox"
          checked={isSelected}
          onChange={(e) => onFundSelection(fund.id, e.target.checked)}
          disabled={!canSelect && !isSelected}
          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          title={!canSelect && !isSelected ? t('funds.maxFundsSelected') : ''}
        />
      ) : null;
    },
    className: 'text-center w-12',
  },
  {
    key: 'name',
    label: 'Fund Name',
    sortable: true,
    format: (value: string, fund: Fund) => (
      <Link href={`/funds/${fund.id}`}>
        <div className="cursor-pointer hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
          <div className="font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">{value}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">{fund.symbol}</div>
        </div>
      </Link>
    ),
  },
  {
    key: 'nav',
    label: 'NAV',
    sortable: true,
    format: (value: number) => formatCurrency(value, 'USD'),
    className: 'text-right',
  },
  {
    key: 'change',
    label: 'Change',
    sortable: true,
    format: (value: number, fund: Fund) => (
      <div className={`text-right ${value >= 0 ? 'text-green-600' : 'text-red-600'}`}>
        <div className="font-medium">
          {value >= 0 ? '+' : ''}{formatCurrency(value, 'USD')}
        </div>
        <div className="text-sm">
          ({fund.changePercent >= 0 ? '+' : ''}{fund.changePercent.toFixed(2)}%)
        </div>
      </div>
    ),
    className: 'text-right',
  },
  {
    key: 'volume',
    label: 'Volume',
    sortable: true,
    format: (value: number) => value.toLocaleString(),
    className: 'text-right',
  },
  {
    key: 'aum',
    label: 'AUM (M)',
    sortable: true,
    format: (value: number) => `$${(value / 1000000).toFixed(1)}M`,
    className: 'text-right',
  },
  {
    key: 'expenseRatio',
    label: 'Expense Ratio',
    sortable: true,
    format: (value: number) => `${value}%`,
    className: 'text-right',
  },
  {
    key: 'rating',
    label: 'Rating',
    sortable: true,
    format: (value: number) => (
      <div className="flex items-center justify-center">
        {Array.from({ length: 5 }, (_, i) => (
          <span
            key={i}
            className={`text-lg ${i < value ? 'text-yellow-400' : 'text-gray-300'}`}
          >
            ★
          </span>
        ))}
      </div>
    ),
    className: 'text-center',
  },
  {
    key: 'type',
    label: 'Type',
    sortable: true,
    format: (value: string) => (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        {value.replace('_', ' ').toUpperCase()}
      </span>
    ),
  },
  {
    key: 'category',
    label: 'Category',
    sortable: true,
  },
  {
    key: 'riskLevel',
    label: 'Risk',
    sortable: true,
    format: (value: string) => {
      const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-red-100 text-red-800',
      };
      return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[value as keyof typeof colors]}`}>
          {value.toUpperCase()}
        </span>
      );
    },
  },
  {
    key: 'minimumInvestment',
    label: 'Min Investment',
    sortable: true,
    format: (value: number) => formatCurrency(value),
    className: 'text-right',
  },
  {
    key: 'fundManager',
    label: 'Fund Manager',
    sortable: true,
  },
  {
    key: 'actions',
    label: 'Actions',
    sortable: false,
    format: (_, fund: Fund) => (
      <div className="flex space-x-2">
        <Link href={`/funds/${fund.id}`}>
          <Button variant="ghost" size="sm">
            View
          </Button>
        </Link>
        <Button variant="ghost" size="sm">
          Invest
        </Button>
      </div>
    ),
    className: 'text-center',
  },
];

export default function FundListTable({
  selectedFunds = [],
  onFundSelection,
  maxSelection = 3
}: FundListTableProps = {}) {
  const dispatch = useAppDispatch();
  const { filteredFunds, loading, filter, selectedColumns, lastUpdated } = useAppSelector(state => state.funds);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(25);
  const { t } = useTranslation();

  const handleSort = (columnKey: string) => {
    if (COLUMN_CONFIGS.find(col => col.key === columnKey)?.sortable) {
      dispatch(setSortBy({ sortBy: columnKey as any }));
    }
  };

  const handleColumnToggle = (columnKey: string) => {
    dispatch(toggleColumn(columnKey));
  };

  const visibleColumns = COLUMN_CONFIGS.filter(col =>
    selectedColumns.includes(col.key as string) ||
    col.key === 'actions' ||
    (col.key === 'select' && onFundSelection)
  );

  const getSortIcon = (columnKey: string) => {
    if (filter.sortBy === columnKey) {
      return filter.sortOrder === 'asc' ? '↑' : '↓';
    }
    return '';
  };

  // Pagination calculations
  const totalItems = filteredFunds.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedFunds = filteredFunds.slice(startIndex, endIndex);

  // Reset to first page when filters change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset pagination when filtered funds change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filteredFunds.length]);

  if (loading && filteredFunds.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500 dark:text-gray-400">Loading funds...</p>
        </div>
      </div>
    );
  }

  if (totalItems === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-2">📊</div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No funds found</h3>
        <p className="text-gray-500 dark:text-gray-400">Try adjusting your search criteria or filters.</p>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Column Selector */}
      <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col">
          <div className="text-sm text-gray-600 dark:text-gray-300">
            Showing {totalItems > 0 ? startIndex + 1 : 0}-{Math.min(endIndex, totalItems)} of {totalItems} funds (max {itemsPerPage} per page)
          </div>
          {lastUpdated && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              Last updated: {new Date(lastUpdated).toLocaleTimeString()}
            </div>
          )}
        </div>
        <div className="relative">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowColumnSelector(!showColumnSelector)}
          >
            Customize Columns ⚙️
          </Button>
          {showColumnSelector && (
            <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-10">
              <div className="p-3">
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Select Columns</h4>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {COLUMN_CONFIGS.filter(col => col.key !== 'actions' && col.key !== 'select').map((col) => (
                    <label key={col.key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={selectedColumns.includes(col.key as string)}
                        onChange={() => handleColumnToggle(col.key as string)}
                        className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{col.label}</span>
                    </label>
                  ))}
                </div>
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="outline"
                    size="sm"
                    fullWidth
                    onClick={() => setShowColumnSelector(false)}
                  >
                    Done
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full" role="table" aria-label="Fund data table">
          <thead className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <tr role="row">
              {visibleColumns.map((col) => (
                <th
                  key={col.key}
                  className={`py-3 px-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${
                    col.sortable ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 focus:bg-gray-100 dark:focus:bg-gray-700 focus:outline-none' : ''
                  } ${col.className || ''}`}
                  onClick={() => col.sortable && handleSort(col.key as string)}
                  onKeyDown={(e) => {
                    if (col.sortable && (e.key === 'Enter' || e.key === ' ')) {
                      e.preventDefault();
                      handleSort(col.key as string);
                    }
                  }}
                  tabIndex={col.sortable ? 0 : -1}
                  role="columnheader"
                  aria-sort={
                    filter.sortBy === col.key 
                      ? filter.sortOrder === 'asc' ? 'ascending' : 'descending'
                      : col.sortable ? 'none' : undefined
                  }
                >
                  <div className="flex items-center space-x-1">
                    <span>{col.label}</span>
                    {col.sortable && (
                      <span className="text-gray-400" aria-hidden="true">
                        {getSortIcon(col.key as string) || '↕'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {paginatedFunds.map((fund, index) => (
              <tr key={fund.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 focus-within:bg-gray-50 dark:focus-within:bg-gray-700" role="row">
                {visibleColumns.map((col) => (
                  <td
                    key={`${fund.id}-${col.key}`}
                    className={`py-4 px-4 whitespace-nowrap ${col.className || ''}`}
                    role="cell"
                  >
                    {col.format
                      ? col.format(fund[col.key as keyof Fund], fund, { selectedFunds, onFundSelection, maxSelection, t })
                      : String(fund[col.key as keyof Fund] || '')
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center text-sm text-gray-700 dark:text-gray-300">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            {/* Page numbers */}
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "primary" : "ghost"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 dark:bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-gray-600 dark:text-gray-300">Updating...</span>
          </div>
        </div>
      )}
    </div>
  );
} 