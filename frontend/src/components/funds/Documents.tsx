import React from 'react';
import { <PERSON>, Button } from '@/components/ui';
import { useTranslation } from '@/i18n/provider';
import { 
  Description, 
  PictureAsPdf, 
  ArticleOutlined,
  CloudDownload,
  DateRange,
  FilePresent
} from '@mui/icons-material';

interface Document {
  id: string;
  title: string;
  type: 'prospectus' | 'annual_report' | 'quarterly_report' | 'fact_sheet' | 'other';
  fileSize: string;
  uploadedDate: string;
  url?: string;
}

interface DocumentsProps {
  fundId: string;
  fundName: string;
}

const Documents: React.FC<DocumentsProps> = ({ fundId, fundName }) => {
  const { t } = useTranslation();
  
  // Mock documents data - in real implementation, this would come from an API
  const documents: Document[] = [
    {
      id: '1',
      title: 'Fund Prospectus 2024',
      type: 'prospectus',
      fileSize: '2.4 MB',
      uploadedDate: '2024-01-15',
      url: '#'
    },
    {
      id: '2',
      title: 'Annual Report 2023',
      type: 'annual_report',
      fileSize: '5.8 MB',
      uploadedDate: '2024-03-10',
      url: '#'
    },
    {
      id: '3',
      title: 'Q3 2024 Quarterly Report',
      type: 'quarterly_report',
      fileSize: '1.2 MB',
      uploadedDate: '2024-10-20',
      url: '#'
    },
    {
      id: '4',
      title: 'Fund Fact Sheet',
      type: 'fact_sheet',
      fileSize: '856 KB',
      uploadedDate: '2024-11-01',
      url: '#'
    }
  ];

  const getDocumentIcon = (type: Document['type']) => {
    switch (type) {
      case 'prospectus':
        return <Description className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
      case 'annual_report':
      case 'quarterly_report':
        return <PictureAsPdf className="w-5 h-5 text-red-600 dark:text-red-400" />;
      case 'fact_sheet':
        return <ArticleOutlined className="w-5 h-5 text-green-600 dark:text-green-400" />;
      default:
        return <FilePresent className="w-5 h-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getDocumentTypeLabel = (type: Document['type']) => {
    switch (type) {
      case 'prospectus':
        return t('funds.documentsSection.prospectus');
      case 'annual_report':
        return t('funds.documentsSection.annualReport');
      case 'quarterly_report':
        return t('funds.documentsSection.quarterlyReport');
      case 'fact_sheet':
        return t('funds.documentsSection.factSheet');
      default:
        return t('funds.documentsSection.other');
    }
  };

  const groupedDocuments = documents.reduce((acc, doc) => {
    if (!acc[doc.type]) {
      acc[doc.type] = [];
    }
    acc[doc.type].push(doc);
    return acc;
  }, {} as Record<Document['type'], Document[]>);

  return (
    <div className="space-y-6">
      {Object.entries(groupedDocuments).map(([type, docs]) => (
        <Card key={type}>
          <Card.Header>
            <Card.Title className="flex items-center gap-2">
              {getDocumentIcon(type as Document['type'])}
              {getDocumentTypeLabel(type as Document['type'])}
            </Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-3">
              {docs.map((doc) => (
                <div 
                  key={doc.id} 
                  className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                >
                  <div className="flex-1 mb-3 sm:mb-0">
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {doc.title}
                    </h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                      <span>{doc.fileSize}</span>
                      <div className="flex items-center gap-1">
                        <DateRange className="w-4 h-4" />
                        <span>{new Date(doc.uploadedDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Handle download
                      if (doc.url) {
                        window.open(doc.url, '_blank');
                      }
                    }}
                  >
                    <CloudDownload className="w-4 h-4 mr-2" />
                    {t('funds.documentsSection.download')}
                  </Button>
                </div>
              ))}
            </div>
          </Card.Content>
        </Card>
      ))}

      {documents.length === 0 && (
        <Card>
          <Card.Content>
            <div className="text-center py-8">
              <FilePresent className="w-12 h-12 text-gray-400 mx-auto mb-3" />
              <p className="text-gray-600 dark:text-gray-400">{t('funds.documentsSection.noDocumentsAvailable')}</p>
            </div>
          </Card.Content>
        </Card>
      )}
    </div>
  );
};

export default Documents;