import { FundDetails, PriceData, ValuationMetrics, TechnicalIndicators } from '@/types';
import Card from '@/components/ui/Card';
import {
  AccountBalance,
  TrendingUp,
  BarChart,
  Assessment,
  Timeline,
  ShowChart,
  Speed,
  Analytics
} from '@mui/icons-material';

interface MarketDataDisplayProps {
  fund: FundDetails;
  className?: string;
}

export default function MarketDataDisplay({ fund, className = '' }: MarketDataDisplayProps) {
  const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';

  const formatCurrency = (amount: number, currency = 'USD') => {
    if (!amount || amount === 0) return ENABLE_MOCK_FALLBACK ? '$0.00' : 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatNumber = (value: number, decimals = 2) => {
    if (!value || value === 0) return ENABLE_MOCK_FALLBACK ? '0.00' : 'N/A';
    return value.toFixed(decimals);
  };

  const formatPercentage = (value: number) => {
    if (!value || value === 0) return ENABLE_MOCK_FALLBACK ? '0.00%' : 'N/A';
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatTimestamp = (timestamp: string | Date) => {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getDataQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20';
      case 'good': return 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20';
      case 'fair': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/20';
      case 'poor': return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20';
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800';
    }
  };

  const priceData = fund.currentPriceData;
  const valuationMetrics = fund.analytics.valuationMetrics;
  const technicalIndicators = fund.analytics.technicalIndicators;

  // Check if we have meaningful valuation or technical data
  const hasValuationData = valuationMetrics && Object.values(valuationMetrics).some(value => value && value !== 0);
  const hasTechnicalData = technicalIndicators && Object.values(technicalIndicators).some(value => value && value !== 0);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Real-time Price Data */}
      {priceData && (
        <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-colors">
          <Card.Header>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <Card.Title>Real-time Market Data</Card.Title>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Last Updated: {formatTimestamp(priceData.asOf)}
              </span>
              {fund.marketDataSummary && (
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDataQualityColor(fund.marketDataSummary.overallQuality)}`}>
                  {fund.marketDataSummary.overallQuality.toUpperCase()}
                </span>
              )}
            </div>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* NAV */}
              {priceData.nav && (
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg transition-colors">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Net Asset Value</div>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {formatCurrency(priceData.nav.value, priceData.nav.currency)}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Source: {priceData.nav.source.replace('_', ' ')}
                  </div>
                </div>
              )}

              {/* Market Price */}
              {priceData.marketPrice && (
                <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg transition-colors">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Market Price</div>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {formatCurrency(priceData.marketPrice.value, priceData.marketPrice.currency)}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    Source: {priceData.marketPrice.source.replace('_', ' ')}
                  </div>
                </div>
              )}

              {/* Volume */}
              {priceData.volume && (
                <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg transition-colors">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Trading Volume</div>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {priceData.volume.toLocaleString()}
                  </div>
                  {priceData.avgVolume30d && (
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      30d Avg: {priceData.avgVolume30d.toLocaleString()}
                    </div>
                  )}
                </div>
              )}

              {/* Bid-Ask Spread */}
              {priceData.bidAskSpreadPct && (
                <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg transition-colors">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bid-Ask Spread</div>
                  <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {formatPercentage(priceData.bidAskSpreadPct)}
                  </div>
                  {priceData.bidAskSpread && (
                    <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      Absolute: {formatCurrency(priceData.bidAskSpread)}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Price Changes */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Daily Performance</div>
                <div className="space-y-2">
                  {priceData.priceChange1d && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">1-Day Change</span>
                      <span className={`font-medium ${priceData.priceChange1d >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {formatCurrency(priceData.priceChange1d)} ({formatPercentage(priceData.priceChange1dPct || 0)})
                      </span>
                    </div>
                  )}
                  {priceData.priceChangeYtd && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">YTD Change</span>
                      <span className={`font-medium ${priceData.priceChangeYtd >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {formatCurrency(priceData.priceChangeYtd)} ({formatPercentage(priceData.priceChangeYtdPct || 0)})
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Market Capitalization</div>
                <div className="space-y-2">
                  {priceData.marketCap && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Market Cap</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(priceData.marketCap)}</span>
                    </div>
                  )}
                  {priceData.sharesOutstanding && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Shares Outstanding</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{priceData.sharesOutstanding.toLocaleString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      )}

      {/* Valuation Metrics */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-colors">
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Assessment className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            <Card.Title>Valuation Metrics</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          {!hasValuationData && !ENABLE_MOCK_FALLBACK ? (
            <div className="text-center py-8">
              <div className="text-gray-500 dark:text-gray-400 mb-2">
                No valuation metrics available
              </div>
              <div className="text-sm text-gray-400 dark:text-gray-500">
                Valuation data has not been calculated for this fund yet.
              </div>
            </div>
          ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Price Ratios */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Price Ratios</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">P/E Ratio</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.priceToEarnings)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">P/B Ratio</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.priceToBook)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">P/S Ratio</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.priceToSales)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">P/CF Ratio</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.priceToCashFlow)}</span>
                </div>
              </div>
            </div>

            {/* Enterprise Value */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Enterprise Value</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Enterprise Value</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(valuationMetrics.enterpriseValue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">EV/Revenue</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.evToRevenue)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">EV/EBITDA</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.evToEbitda)}</span>
                </div>
              </div>
            </div>

            {/* Profitability & Returns */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Profitability</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">ROE</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatPercentage(valuationMetrics.returnOnEquity)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">ROA</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatPercentage(valuationMetrics.returnOnAssets)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Debt/Equity</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(valuationMetrics.debtToEquity)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Dividend Yield</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatPercentage(valuationMetrics.dividendYield)}</span>
                </div>
              </div>
            </div>
          </div>
          )}
        </Card.Content>
      </Card>

      {/* Technical Indicators */}
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-colors">
        <Card.Header>
          <div className="flex items-center space-x-2">
            <ShowChart className="w-5 h-5 text-green-600 dark:text-green-400" />
            <Card.Title>Technical Indicators</Card.Title>
          </div>
        </Card.Header>
        <Card.Content>
          {!hasTechnicalData && !ENABLE_MOCK_FALLBACK ? (
            <div className="text-center py-8">
              <div className="text-gray-500 dark:text-gray-400 mb-2">
                No technical indicators available
              </div>
              <div className="text-sm text-gray-400 dark:text-gray-500">
                Technical analysis data has not been calculated for this fund yet.
              </div>
            </div>
          ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Moving Averages */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Moving Averages</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">SMA 20</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.sma20)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">SMA 50</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.sma50)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">SMA 200</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.sma200)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">VWAP</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.vwap)}</span>
                </div>
              </div>
            </div>

            {/* Momentum Indicators */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Momentum</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">RSI (14)</span>
                  <span className={`font-medium ${
                    technicalIndicators.rsi14 > 70 ? 'text-red-600 dark:text-red-400' :
                    technicalIndicators.rsi14 < 30 ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-gray-100'
                  }`}>
                    {formatNumber(technicalIndicators.rsi14)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">MACD Line</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(technicalIndicators.macdLine, 3)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">MACD Signal</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(technicalIndicators.macdSignal, 3)}</span>
                </div>
              </div>
            </div>

            {/* Support & Resistance */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Support & Resistance</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Support Level</span>
                  <span className="font-medium text-green-600 dark:text-green-400">{formatCurrency(technicalIndicators.supportLevel)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Resistance Level</span>
                  <span className="font-medium text-red-600 dark:text-red-400">{formatCurrency(technicalIndicators.resistanceLevel)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Bollinger Upper</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.bollingerUpper)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Bollinger Lower</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{formatCurrency(technicalIndicators.bollingerLower)}</span>
                </div>
              </div>
            </div>
          </div>
          )}
        </Card.Content>
      </Card>
    </div>
  );
}
