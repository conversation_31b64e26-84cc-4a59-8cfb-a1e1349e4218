import { FundDetails } from '@/types';
import Card from '@/components/ui/Card';
import {
  Security,
  Assessment,
  TrendingDown,
  Analytics,
  Warning,
  Timeline
} from '@mui/icons-material';

interface RiskAnalyticsDisplayProps {
  fund: FundDetails;
  className?: string;
}

export default function RiskAnalyticsDisplay({ fund, className = '' }: RiskAnalyticsDisplayProps) {
  const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';

  const formatPercentage = (value: number | undefined) => {
    if (value === undefined || value === null || isNaN(value)) {
      return 'N/A';
    }
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatNumber = (value: number | undefined, decimals = 2) => {
    if (value === undefined || value === null || isNaN(value)) {
      return 'N/A';
    }
    return value.toFixed(decimals);
  };

  const getRiskColor = (value: number | undefined, type: 'var' | 'ratio' | 'volatility' | 'drawdown') => {
    if (value === undefined || value === null || isNaN(value)) {
      return 'text-gray-500 dark:text-gray-400';
    }
    switch (type) {
      case 'var':
        return value < -10 ? 'text-red-600 dark:text-red-400' : value < -5 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400';
      case 'ratio':
        return value > 1 ? 'text-green-600 dark:text-green-400' : value > 0.5 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400';
      case 'volatility':
        return value > 25 ? 'text-red-600 dark:text-red-400' : value > 15 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400';
      case 'drawdown':
        return value < -20 ? 'text-red-600 dark:text-red-400' : value < -10 ? 'text-yellow-600 dark:text-yellow-400' : 'text-green-600 dark:text-green-400';
      default:
        return 'text-gray-900 dark:text-gray-100';
    }
  };

  const riskMetrics = fund.analytics.riskMetrics;
  const kpis = fund.analytics.kpis;

  // Check if analytics data is available or meaningful
  const hasAnalyticsData = kpis && Object.values(kpis).some(value => value && value !== 0);

  if (!hasAnalyticsData && !ENABLE_MOCK_FALLBACK) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <Card.Header>
            <div className="flex items-center space-x-2">
              <Security className="w-5 h-5 text-gray-400" />
              <Card.Title>Risk Analytics</Card.Title>
            </div>
          </Card.Header>
          <Card.Content>
            <div className="text-center py-12">
              <div className="text-gray-500 dark:text-gray-400 mb-2">
                No risk analytics data available
              </div>
              <div className="text-sm text-gray-400 dark:text-gray-500">
                Risk analytics data has not been calculated for this fund yet.
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Value at Risk */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Security className="w-5 h-5 text-red-600 dark:text-red-400" />
            <Card.Title>Value at Risk (VaR)</Card.Title>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Potential loss estimates at different confidence levels
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">1-Day VaR (95%)</div>
              <div className={`text-2xl font-bold ${getRiskColor(riskMetrics.var1d95, 'var')}`}>
                {formatPercentage(riskMetrics.var1d95)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                95% confidence level
              </div>
            </div>

            <div className="bg-red-100 dark:bg-red-900/30 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">1-Day VaR (99%)</div>
              <div className={`text-2xl font-bold ${getRiskColor(riskMetrics.var1d99, 'var')}`}>
                {formatPercentage(riskMetrics.var1d99)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                99% confidence level
              </div>
            </div>

            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">CVaR (95%)</div>
              <div className={`text-2xl font-bold ${getRiskColor(riskMetrics.cvar1d95, 'var')}`}>
                {formatPercentage(riskMetrics.cvar1d95)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Expected shortfall
              </div>
            </div>

            <div className="bg-orange-100 dark:bg-orange-900/30 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">CVaR (99%)</div>
              <div className={`text-2xl font-bold ${getRiskColor(riskMetrics.cvar1d99, 'var')}`}>
                {formatPercentage(riskMetrics.cvar1d99)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Expected shortfall
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Risk-Adjusted Returns */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Assessment className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            <Card.Title>Risk-Adjusted Return Metrics</Card.Title>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Performance measures that account for risk taken
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Sharpe Ratio */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sharpe Ratio</div>
              <div className={`text-2xl font-bold ${getRiskColor(kpis.sharpeRatio, 'ratio')}`}>
                {formatNumber(kpis.sharpeRatio)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Risk-adjusted return
              </div>
            </div>

            {/* Sortino Ratio */}
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sortino Ratio</div>
              <div className={`text-2xl font-bold ${getRiskColor(kpis.sortinoRatio, 'ratio')}`}>
                {formatNumber(kpis.sortinoRatio)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Downside risk-adjusted
              </div>
            </div>

            {/* Calmar Ratio */}
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Calmar Ratio</div>
              <div className={`text-2xl font-bold ${getRiskColor(kpis.calmarRatio, 'ratio')}`}>
                {formatNumber(kpis.calmarRatio)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Return vs max drawdown
              </div>
            </div>

            {/* Information Ratio */}
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Information Ratio</div>
              <div className={`text-2xl font-bold ${getRiskColor(kpis.informationRatio, 'ratio')}`}>
                {formatNumber(kpis.informationRatio)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Active return vs tracking error
              </div>
            </div>

            {/* Treynor Ratio */}
            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg transition-colors">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Treynor Ratio</div>
              <div className={`text-2xl font-bold ${getRiskColor(kpis.treynorRatio, 'ratio')}`}>
                {formatNumber(kpis.treynorRatio, 3)}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Return per unit of systematic risk
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Volatility and Drawdown Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Volatility Metrics */}
        <Card>
          <Card.Header>
            <div className="flex items-center space-x-2">
              <TrendingDown className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              <Card.Title>Volatility Analysis</Card.Title>
            </div>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Standard Deviation</span>
                <span className={`font-medium ${getRiskColor(riskMetrics.standardDeviation, 'volatility')}`}>
                  {formatPercentage(riskMetrics.standardDeviation)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Downside Deviation</span>
                <span className={`font-medium ${getRiskColor(riskMetrics.downsideDeviation, 'volatility')}`}>
                  {formatPercentage(riskMetrics.downsideDeviation)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Downside Risk</span>
                <span className={`font-medium ${getRiskColor(riskMetrics.downSideRisk, 'volatility')}`}>
                  {formatPercentage(riskMetrics.downSideRisk)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Tracking Error</span>
                <span className={`font-medium ${getRiskColor(kpis.trackingError, 'volatility')}`}>
                  {formatPercentage(kpis.trackingError)}
                </span>
              </div>
            </div>
          </Card.Content>
        </Card>

        {/* Drawdown Analysis */}
        <Card>
          <Card.Header>
            <div className="flex items-center space-x-2">
              <Warning className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              <Card.Title>Drawdown Analysis</Card.Title>
            </div>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Maximum Drawdown</span>
                <span className={`font-medium ${getRiskColor(kpis.maxDrawdown, 'drawdown')}`}>
                  {formatPercentage(kpis.maxDrawdown)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600 dark:text-gray-400">Current Drawdown</span>
                <span className={`font-medium ${getRiskColor(riskMetrics.varRisk, 'drawdown')}`}>
                  {formatPercentage(riskMetrics.varRisk)}
                </span>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg mt-4 transition-colors">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Risk Assessment</div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {kpis.maxDrawdown > -10 ?
                    "Low drawdown risk - Fund shows good downside protection" :
                    kpis.maxDrawdown > -20 ?
                    "Moderate drawdown risk - Monitor during market stress" :
                    "High drawdown risk - Consider position sizing carefully"
                  }
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Market Risk Metrics */}
      <Card>
        <Card.Header>
          <div className="flex items-center space-x-2">
            <Timeline className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            <Card.Title>Market Risk Metrics</Card.Title>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Relationship with market and benchmark performance
          </p>
        </Card.Header>
        <Card.Content>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Market Sensitivity</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Beta</span>
                  <span className={`font-medium ${
                    kpis.beta > 1.2 ? 'text-red-600 dark:text-red-400' :
                    kpis.beta < 0.8 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-gray-100'
                  }`}>
                    {formatNumber(kpis.beta)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Alpha</span>
                  <span className={`font-medium ${kpis.alpha >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {formatPercentage(kpis.alpha)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Correlation</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {formatNumber(riskMetrics.correlation)}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Risk Interpretation</h4>
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <div>
                  <strong>Beta {formatNumber(kpis.beta)}:</strong> {
                    kpis.beta > 1.2 ? 'High market sensitivity' :
                    kpis.beta < 0.8 ? 'Low market sensitivity' :
                    'Moderate market sensitivity'
                  }
                </div>
                <div>
                  <strong>Alpha {formatPercentage(kpis.alpha)}:</strong> {
                    kpis.alpha > 2 ? 'Strong outperformance' :
                    kpis.alpha > 0 ? 'Positive excess return' :
                    'Underperforming benchmark'
                  }
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">Risk Summary</h4>
              <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg transition-colors">
                <div className="text-sm">
                  <div className="font-medium mb-1 text-gray-900 dark:text-gray-100">Overall Risk Level</div>
                  <div className={`text-xs ${
                    riskMetrics.standardDeviation > 20 ? 'text-red-600 dark:text-red-400' :
                    riskMetrics.standardDeviation > 15 ? 'text-yellow-600 dark:text-yellow-400' :
                    'text-green-600 dark:text-green-400'
                  }`}>
                    {riskMetrics.standardDeviation > 20 ? 'HIGH RISK' :
                     riskMetrics.standardDeviation > 15 ? 'MODERATE RISK' :
                     'LOW RISK'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}
