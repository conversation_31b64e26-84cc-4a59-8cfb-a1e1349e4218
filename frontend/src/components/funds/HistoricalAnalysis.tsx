'use client';

import { useState, useEffect } from 'react';
import { FundDetails, TimePeriod, ChartDataPoint } from '@/types';
import { fundApi } from '@/lib/api';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import TimePeriodSelector from './TimePeriodSelector';
import PerformanceChart from './PerformanceChart';
import { Timeline } from '@mui/icons-material';

interface HistoricalAnalysisProps {
  fund: FundDetails;
  className?: string;
}

interface AnalysisMetrics {
  returns: {
    period: number;
    annualized: number;
    cumulative: number;
  };
  risk: {
    volatility: number;
    maxDrawdown: number;
    sharpeRatio: number;
    betaVsBenchmark?: number;
  };
  comparison?: {
    outperformance: number;
    correlationWithBenchmark: number;
  };
}

export default function HistoricalAnalysis({ fund, className = '' }: HistoricalAnalysisProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('1Y');
  const [historicalData, setHistoricalData] = useState<ChartDataPoint[]>(fund.historicalData);
  const [benchmarkData, setBenchmarkData] = useState<ChartDataPoint[]>([]);
  const [analysisMetrics, setAnalysisMetrics] = useState<AnalysisMetrics | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeView, setActiveView] = useState<'performance' | 'risk' | 'comparison'>('performance');

  // Fetch data for the selected time period
  const fetchPeriodData = async (period: TimePeriod) => {
    try {
      setLoading(true);
      
      // Fetch fund historical data
      const fundResponse = await fundApi.getFundHistoricalData(fund.id, period);
      setHistoricalData(fundResponse.data);
      
      // Only generate benchmark data if mock mode is enabled
      const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';
      let currentBenchmarkData: ChartDataPoint[] = [];
      
      if (fund.benchmark && ENABLE_MOCK_FALLBACK) {
        try {
          // For demo purposes, generate similar data with slight variations (only in mock mode)
          const mockBenchmarkData = fundResponse.data.map((point, index) => ({
            ...point,
            value: point.value * (0.95 + Math.random() * 0.1), // Vary benchmark by ±5%
          }));
          setBenchmarkData(mockBenchmarkData);
          currentBenchmarkData = mockBenchmarkData;
          console.log('📊 Generated mock benchmark data (ENABLE_MOCK_FALLBACK=true)');
        } catch (err) {
          console.warn('Failed to generate benchmark data:', err);
          setBenchmarkData([]);
        }
      } else if (fund.benchmark && !ENABLE_MOCK_FALLBACK) {
        console.log('🚫 Skipping benchmark data generation (ENABLE_MOCK_FALLBACK=false)');
        setBenchmarkData([]);
      }
      
      // Calculate analysis metrics using the correct benchmark data
      calculateAnalysisMetrics(fundResponse.data, currentBenchmarkData);
      
    } catch (err) {
      console.error('Failed to fetch period data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Calculate advanced metrics
  const calculateAnalysisMetrics = (fundData: ChartDataPoint[], benchmarkData: ChartDataPoint[]) => {
    if (fundData.length < 2) return;

    const returns = fundData.map((point, index) => {
      if (index === 0) return 0;
      return ((point.value - fundData[index - 1].value) / fundData[index - 1].value) * 100;
    }).slice(1);

    const benchmarkReturns = benchmarkData.length > 1 ? benchmarkData.map((point, index) => {
      if (index === 0) return 0;
      return ((point.value - benchmarkData[index - 1].value) / benchmarkData[index - 1].value) * 100;
    }).slice(1) : [];

    // Calculate metrics
    const periodReturn = ((fundData[fundData.length - 1].value - fundData[0].value) / fundData[0].value) * 100;
    const daysInPeriod = fundData.length;
    const annualizedReturn = Math.pow(1 + (periodReturn / 100), 365 / daysInPeriod) - 1;
    
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance) * Math.sqrt(252); // Annualized volatility
    
    let maxDrawdown = 0;
    let peak = fundData[0].value;
    fundData.forEach(point => {
      if (point.value > peak) peak = point.value;
      const drawdown = ((peak - point.value) / peak) * 100;
      if (drawdown > maxDrawdown) maxDrawdown = drawdown;
    });

    const riskFreeRate = 6; // Assume 6% risk-free rate for India
    const sharpeRatio = volatility > 0 ? (annualizedReturn * 100 - riskFreeRate) / volatility : 0;

    const metrics: AnalysisMetrics = {
      returns: {
        period: periodReturn,
        annualized: annualizedReturn * 100,
        cumulative: periodReturn,
      },
      risk: {
        volatility,
        maxDrawdown,
        sharpeRatio,
      },
    };

    // Add comparison metrics if benchmark data available
    if (benchmarkReturns.length > 0) {
      const benchmarkPeriodReturn = ((benchmarkData[benchmarkData.length - 1].value - benchmarkData[0].value) / benchmarkData[0].value) * 100;
      const outperformance = periodReturn - benchmarkPeriodReturn;
      
      // Calculate correlation
      const meanBenchmark = benchmarkReturns.reduce((sum, ret) => sum + ret, 0) / benchmarkReturns.length;
      const covariance = returns.reduce((sum, ret, i) => {
        return sum + ((ret - mean) * (benchmarkReturns[i] - meanBenchmark));
      }, 0) / returns.length;
      
      const benchmarkVariance = benchmarkReturns.reduce((sum, ret) => sum + Math.pow(ret - meanBenchmark, 2), 0) / benchmarkReturns.length;
      const correlation = covariance / Math.sqrt(variance * benchmarkVariance);
      
      // Calculate beta
      const beta = covariance / benchmarkVariance;
      
      metrics.comparison = {
        outperformance,
        correlationWithBenchmark: correlation,
      };
      metrics.risk.betaVsBenchmark = beta;
    }

    setAnalysisMetrics(metrics);
  };

  // Handle period change
  const handlePeriodChange = async (period: TimePeriod) => {
    setSelectedPeriod(period);
    await fetchPeriodData(period);
  };

  // Initialize with default period
  useEffect(() => {
    const ENABLE_MOCK_FALLBACK = process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true';
    
    // Only use benchmark data for calculations if mock mode is enabled
    const initialBenchmarkData = (fund.benchmark && ENABLE_MOCK_FALLBACK) ? benchmarkData : [];
    calculateAnalysisMetrics(historicalData, initialBenchmarkData);
  }, []);

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatNumber = (value: number, decimals: number = 2) => {
    return value.toFixed(decimals);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 transition-colors">
        <Card.Header>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center space-x-2">
              <Timeline className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <Card.Title>Historical Analysis</Card.Title>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              {/* View Toggle */}
              <div className="flex gap-1">
                {[
                  { key: 'performance', label: 'Performance' },
                  { key: 'risk', label: 'Risk' },
                  ...(fund.benchmark && process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true' ? [{ key: 'comparison', label: 'Comparison' }] : []),
                ].map((view) => (
                  <Button
                    key={view.key}
                    variant={activeView === view.key ? 'primary' : 'outline'}
                    size="sm"
                    onClick={() => setActiveView(view.key as any)}
                  >
                    {view.label}
                  </Button>
                ))}
              </div>
              
              {/* Time Period Selector */}
              <TimePeriodSelector
                selectedPeriod={selectedPeriod}
                onPeriodChange={handlePeriodChange}
                loading={loading}
              />
            </div>
          </div>
        </Card.Header>
        
        <Card.Content>
          {/* Performance Chart */}
          <div className="mb-8">
            <PerformanceChart
              fundData={historicalData}
              benchmarkData={fund.benchmark && process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK === 'true' ? benchmarkData : undefined}
              fundName={fund.name}
              benchmarkName={fund.benchmark?.name}
              loading={loading}
              onPeriodChange={handlePeriodChange}
            />
          </div>
          
          {/* Analysis Metrics */}
          {analysisMetrics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Performance Metrics */}
              {activeView === 'performance' && (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Returns Analysis</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Period Return</span>
                      <span className={`font-medium ${
                        analysisMetrics.returns.period >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatPercentage(analysisMetrics.returns.period)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Annualized Return</span>
                      <span className={`font-medium ${
                        analysisMetrics.returns.annualized >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatPercentage(analysisMetrics.returns.annualized)}
                      </span>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Risk Metrics */}
              {activeView === 'risk' && (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Risk Analysis</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Volatility (Annualized)</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">{formatPercentage(analysisMetrics.risk.volatility)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Max Drawdown</span>
                      <span className="font-medium text-red-600 dark:text-red-400">
                        -{formatPercentage(Math.abs(analysisMetrics.risk.maxDrawdown))}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Sharpe Ratio</span>
                      <span className={`font-medium ${
                        analysisMetrics.risk.sharpeRatio >= 1 ? 'text-green-600 dark:text-green-400' :
                        analysisMetrics.risk.sharpeRatio >= 0.5 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatNumber(analysisMetrics.risk.sharpeRatio)}
                      </span>
                    </div>
                    {analysisMetrics.risk.betaVsBenchmark && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Beta vs Benchmark</span>
                        <span className="font-medium text-gray-900 dark:text-gray-100">{formatNumber(analysisMetrics.risk.betaVsBenchmark)}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              {/* Comparison Metrics */}
              {activeView === 'comparison' && analysisMetrics.comparison && (
                <div className="space-y-4">
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100">Benchmark Comparison</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Outperformance</span>
                      <span className={`font-medium ${
                        analysisMetrics.comparison.outperformance >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {formatPercentage(analysisMetrics.comparison.outperformance)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Correlation</span>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {formatNumber(analysisMetrics.comparison.correlationWithBenchmark)}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </Card.Content>
      </Card>
    </div>
  );
} 