import React, { useCallback, useMemo } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Fund, FundDetails } from '@/types';
import Card from '@/components/ui/Card';

// Zod validation schema
const fundFormSchema = z.object({
  // Basic Information - Required fields
  name: z.string()
    .min(1, 'Fund name is required')
    .max(200, 'Fund name must be less than 200 characters')
    .trim(),
  symbol: z.string()
    .min(1, 'Fund symbol is required')
    .max(20, 'Fund symbol must be less than 20 characters')
    .regex(/^[A-Z0-9._-]+$/i, 'Symbol can only contain letters, numbers, dots, hyphens, and underscores')
    .trim(),
  type: z.enum(['mutual_fund', 'etf', 'index_fund', 'bond_fund', 'money_market'], {
    required_error: 'Fund type is required'
  }),
  category: z.string()
    .min(1, 'Category is required')
    .max(100, 'Category must be less than 100 characters')
    .trim(),
  subCategory: z.string()
    .max(100, 'Sub-category must be less than 100 characters')
    .trim()
    .optional(),
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .trim()
    .optional(),
  
  // Financial Details - All as optional strings
  nav: z.string().optional(),
  previousNav: z.string().optional(),
  minimumInvestment: z.string().optional(),
  expenseRatio: z.string().optional(),
  aum: z.string().optional(),
  rating: z.string().optional(),
  
  // Management Information
  fundManager: z.string()
    .min(1, 'Fund manager is required')
    .max(100, 'Fund manager name must be less than 100 characters')
    .trim(),
  inceptionDate: z.string()
    .min(1, 'Inception date is required')
    .refine((val) => {
      if (!val || val.trim() === '') return false; // Required field
      const date = new Date(val);
      return !isNaN(date.getTime()) && date <= new Date();
    }, {
      message: 'Inception date must be a valid date and cannot be in the future'
    }),
  
  // Risk Classification
  riskLevel: z.enum(['low', 'medium', 'high'], {
    required_error: 'Risk level is required'
  }),
});

type FundFormData = z.infer<typeof fundFormSchema>;

interface FundEditFormProps {
  fund?: Fund | FundDetails;
  isEditing?: boolean;
  onSubmit: (fundData: Partial<Fund>) => void;
  onCancel: () => void;
  loading?: boolean;
}

// Move input field components outside to prevent recreation on every render
const ControlledInputField = React.memo(({
  name,
  label,
  type = 'text',
  required = false,
  placeholder = '',
  step,
  min,
  max,
  control,
  errors,
  touchedFields,
  loading,
  clearErrors,
  trigger
}: {
  name: keyof FundFormData;
  label: string;
  type?: string;
  required?: boolean;
  placeholder?: string;
  step?: string;
  min?: string;
  max?: string;
  control: any;
  errors: any;
  touchedFields: any;
  loading: boolean;
  clearErrors: any;
  trigger: any;
}) => (
  <div>
    <label htmlFor={name} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <input
          {...field}
          type={type}
          id={name}
          placeholder={placeholder}
          step={step}
          min={min}
          max={max}
          className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${
            errors[name] ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' :
            touchedFields[name] && !errors[name] ? 'border-green-300 dark:border-green-600 focus:ring-green-500 focus:border-green-500' :
            'border-gray-300 dark:border-gray-600'
          }`}
          disabled={loading}
          onFocus={() => {
            // Don't clear errors on focus - let them persist until field is valid
            // This ensures validation errors remain visible while user is correcting them
          }}
          onChange={(e) => {
            field.onChange(e);
            // Clear error only when user starts typing and field becomes valid
            // Trigger validation after a short delay to provide immediate feedback
            setTimeout(() => trigger(name), 100);
          }}
          onBlur={(e) => {
            field.onBlur();
            // Trigger validation on blur for feedback
            trigger(name);
          }}
        />
      )}
    />
    {errors[name] && (
      <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center font-medium bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-md border border-red-200 dark:border-red-800">
        <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        {errors[name]?.message}
      </p>
    )}
  </div>
));

export default function FundEditForm({ 
  fund, 
  isEditing = false, 
  onSubmit, 
  onCancel, 
  loading = false 
}: FundEditFormProps) {
  const defaultValues = useMemo(() => ({
    name: fund?.name || '',
    symbol: fund?.symbol || '',
    type: fund?.type || 'mutual_fund',
    category: fund?.category || '',
    subCategory: fund?.subCategory || '',
    description: fund?.description || '',
    nav: fund?.nav?.toString() || '',
    previousNav: fund?.previousNav?.toString() || '',
    minimumInvestment: fund?.minimumInvestment?.toString() || '',
    expenseRatio: fund?.expenseRatio?.toString() || '',
    aum: fund?.aum?.toString() || '',
    rating: fund?.rating?.toString() || '',
    fundManager: fund?.fundManager || '',
    inceptionDate: fund?.inceptionDate ? new Date(fund.inceptionDate).toISOString().split('T')[0] : '',
    riskLevel: fund?.riskLevel || 'medium',
  }), [fund]);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isDirty, touchedFields },
    trigger,
    clearErrors,
    setError
  } = useForm<FundFormData>({
    resolver: zodResolver(fundFormSchema),
    mode: 'onChange', // Validate on change for better UX
    defaultValues
  });

  // Custom validation functions - memoized to prevent re-creation
  const validateNumericField = useCallback((value: string | undefined, fieldName: string): string | undefined => {
    if (!value || value.trim() === '') return undefined;

    const num = parseFloat(value);
    if (isNaN(num)) return `${fieldName} must be a valid number`;

    if (fieldName === 'NAV' && num <= 0) return 'NAV must be a positive number';
    if (fieldName !== 'NAV' && num < 0) return `${fieldName} must be non-negative`;
    if (fieldName === 'Expense Ratio' && num > 100) return 'Expense ratio must be 100% or less';
    if (fieldName === 'Rating' && (num < 1 || num > 5)) return 'Rating must be between 1 and 5';

    return undefined;
  }, []);

  // Note: Date validation is now handled by Zod schema

  const onFormSubmit = useCallback((data: FundFormData) => {
    console.log('Form submitted with data:', data);

    // Don't clear all errors - let Zod schema validation errors persist
    // Only clear manual errors that we're about to re-validate
    const fieldsToValidate = ['nav', 'previousNav', 'minimumInvestment', 'expenseRatio', 'aum', 'rating'];
    fieldsToValidate.forEach(field => clearErrors(field as keyof FundFormData));

    // Client-side validation for numeric fields
    const numericErrors: { [key: string]: string } = {};

    const navError = validateNumericField(data.nav, 'NAV');
    if (navError) numericErrors.nav = navError;

    const prevNavError = validateNumericField(data.previousNav, 'Previous NAV');
    if (prevNavError) numericErrors.previousNav = prevNavError;

    const minInvestError = validateNumericField(data.minimumInvestment, 'Minimum Investment');
    if (minInvestError) numericErrors.minimumInvestment = minInvestError;

    const expenseError = validateNumericField(data.expenseRatio, 'Expense Ratio');
    if (expenseError) numericErrors.expenseRatio = expenseError;

    const aumError = validateNumericField(data.aum, 'AUM');
    if (aumError) numericErrors.aum = aumError;

    const ratingError = validateNumericField(data.rating, 'Rating');
    if (ratingError) numericErrors.rating = ratingError;

    // Note: Inception date validation is handled by Zod schema

    // If there are validation errors, set them in the form and don't submit
    if (Object.keys(numericErrors).length > 0) {
      console.error('Validation errors:', numericErrors);

      // Set each error in the form to display in UI
      Object.entries(numericErrors).forEach(([field, message]) => {
        console.log(`Setting error for field ${field}:`, message);
        setError(field as keyof FundFormData, {
          type: 'manual',
          message: message
        });
      });

      return;
    }

    // Convert form data to Fund format
    const fundData: Partial<Fund> = {
      name: data.name,
      symbol: data.symbol.toUpperCase(),
      type: data.type,
      category: data.category,
      subCategory: data.subCategory || undefined,
      description: data.description || undefined,
      nav: data.nav ? parseFloat(data.nav) : undefined,
      previousNav: data.previousNav ? parseFloat(data.previousNav) : undefined,
      minimumInvestment: data.minimumInvestment ? parseFloat(data.minimumInvestment) : undefined,
      expenseRatio: data.expenseRatio ? parseFloat(data.expenseRatio) : undefined,
      aum: data.aum ? parseFloat(data.aum) : undefined,
      rating: data.rating ? parseFloat(data.rating) : undefined,
      fundManager: data.fundManager,
      inceptionDate: data.inceptionDate ? new Date(data.inceptionDate) : undefined,
      riskLevel: data.riskLevel,
    };

    onSubmit(fundData);
  }, [validateNumericField, onSubmit, setError, clearErrors]);

  const fundTypeOptions = useMemo(() => [
    { value: 'mutual_fund', label: 'Mutual Fund' },
    { value: 'etf', label: 'ETF' },
    { value: 'index_fund', label: 'Index Fund' },
    { value: 'bond_fund', label: 'Bond Fund' },
    { value: 'money_market', label: 'Money Market' },
  ], []);

  const riskLevelOptions = useMemo(() => [
    { value: 'low', label: 'Low Risk' },
    { value: 'medium', label: 'Medium Risk' },
    { value: 'high', label: 'High Risk' },
  ], []);



  const ControlledSelectField = React.memo(({
    name,
    label,
    options,
    required = false
  }: {
    name: keyof FundFormData;
    label: string;
    options: { value: string; label: string }[];
    required?: boolean;
  }) => (
    <div>
      <label htmlFor={name} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <select
            {...field}
            id={name}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${
              errors[name] ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' :
              touchedFields[name] && !errors[name] ? 'border-green-300 dark:border-green-600 focus:ring-green-500 focus:border-green-500' :
              'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
            onChange={(e) => {
              field.onChange(e);
              // Validation will happen on blur/submit
            }}
            onBlur={(e) => {
              field.onBlur();
              // Trigger validation on blur for feedback
              trigger(name);
            }}
          >
            <option value="">Select {label}</option>
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        )}
      />
      {errors[name] && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center font-medium bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-md border border-red-200 dark:border-red-800">
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {errors[name]?.message}
        </p>
      )}
    </div>
  ));

  const ControlledTextAreaField = React.memo(({
    name,
    label,
    required = false,
    placeholder = '',
    rows = 3
  }: {
    name: keyof FundFormData;
    label: string;
    required?: boolean;
    placeholder?: string;
    rows?: number;
  }) => (
    <div>
      <label htmlFor={name} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <textarea
            {...field}
            id={name}
            placeholder={placeholder}
            rows={rows}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical transition-colors bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 ${
              errors[name] ? 'border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500' :
              touchedFields[name] && !errors[name] ? 'border-green-300 dark:border-green-600 focus:ring-green-500 focus:border-green-500' :
              'border-gray-300 dark:border-gray-600'
            }`}
            disabled={loading}
            onChange={(e) => {
              field.onChange(e);
              // Validation will happen on blur/submit
            }}
            onBlur={(e) => {
              field.onBlur();
              // Trigger validation on blur for feedback
              trigger(name);
            }}
          />
        )}
      />
      {errors[name] && (
        <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center font-medium bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-md border border-red-200 dark:border-red-800">
          <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {errors[name]?.message}
        </p>
      )}
    </div>
  ));

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {isEditing ? 'Edit Fund' : 'Create New Fund'}
        </h1>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {isEditing 
            ? 'Update fund information. Required fields are marked with an asterisk (*).' 
            : 'Fill in the details to create a new fund. Required fields are marked with an asterisk (*).'}
        </p>
        {/* Form status indicators - memoized to prevent re-renders */}
        {useMemo(() => (
          <div className="mt-2 flex items-center space-x-4">
            {Object.keys(errors).length > 0 && (
              <div className="flex items-center text-xs text-red-600 dark:text-red-400">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Please fix validation errors before submitting
              </div>
            )}
            {isDirty && (
              <div className="flex items-center text-xs text-amber-600 dark:text-amber-400">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Unsaved changes
              </div>
            )}
            {!Object.keys(errors).length && !isDirty && (
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                No changes made
              </div>
            )}
          </div>
        ), [errors, isDirty])}
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Basic Information Section */}
        <Card>
          <Card.Header>
            <Card.Title>Basic Information</Card.Title>
            <Card.Description>
              Essential fund identification and classification details
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ControlledInputField
                name="name"
                label="Fund Name"
                required
                placeholder="Enter fund name"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="symbol"
                label="Symbol"
                required
                placeholder="Enter fund symbol"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledSelectField
                name="type"
                label="Fund Type"
                options={fundTypeOptions}
                required
              />
              <ControlledInputField
                name="category"
                label="Category"
                required
                placeholder="Enter fund category"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="subCategory"
                label="Sub Category"
                placeholder="Enter sub category (optional)"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledSelectField
                name="riskLevel"
                label="Risk Level"
                options={riskLevelOptions}
                required
              />
            </div>
            <div className="mt-6">
              <ControlledTextAreaField
                name="description"
                label="Description"
                placeholder="Provide a detailed description of the fund's investment objective and strategy"
                rows={4}
              />
            </div>
          </Card.Content>
        </Card>

        {/* Financial Details Section */}
        <Card>
          <Card.Header>
            <Card.Title>Financial Details</Card.Title>
            <Card.Description>
              Current financial metrics and investment parameters
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ControlledInputField
                name="nav"
                label="Current NAV"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="previousNav"
                label="Previous NAV"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="minimumInvestment"
                label="Minimum Investment"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="expenseRatio"
                label="Expense Ratio (%)"
                type="number"
                step="0.01"
                min="0"
                max="100"
                placeholder="0.00"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="aum"
                label="Assets Under Management"
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="rating"
                label="Rating (1-5)"
                type="number"
                step="0.1"
                min="1"
                max="5"
                placeholder="0.0"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
            </div>
          </Card.Content>
        </Card>

        {/* Management Information Section */}
        <Card>
          <Card.Header>
            <Card.Title>Management Information</Card.Title>
            <Card.Description>
              Fund management and operational details
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ControlledInputField
                name="fundManager"
                label="Fund Manager"
                required
                placeholder="Enter fund manager's name"
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
              <ControlledInputField
                name="inceptionDate"
                label="Inception Date"
                type="date"
                required
                control={control}
                errors={errors}
                touchedFields={touchedFields}
                loading={loading}
                clearErrors={clearErrors}
                trigger={trigger}
              />
            </div>
          </Card.Content>
        </Card>

        {/* Form Actions */}
        <Card>
          <Card.Content>
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={onCancel}
                disabled={loading}
                className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {loading && (
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {loading ? 'Saving...' : (isEditing ? 'Update Fund' : 'Create Fund')}
              </button>
            </div>
          </Card.Content>
        </Card>
      </form>
    </div>
  );
} 