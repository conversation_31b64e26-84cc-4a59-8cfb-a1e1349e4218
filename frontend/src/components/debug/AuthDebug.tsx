'use client'

import { useEffect, useState } from 'react'

export default function AuthDebug() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    console.log('🔍 AuthDebug Component - Environment Variables:')
    console.log('NEXT_PUBLIC_ENABLE_MOCK_LOGIN:', process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN)
    console.log('NEXT_PUBLIC_ENABLE_MOCK_FALLBACK:', process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK)
    console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL)

    // Check if auth.ts module has been loaded and environment override applied
    import('@/lib/auth').then((authModule) => {
      console.log('🔍 Auth module loaded, checking configuration...')
      console.log('Auth options:', authModule.authOptions)
    }).catch(console.error)
  }, [])

  if (!mounted) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
        <h3 className="font-bold text-yellow-800">Debug Info</h3>
        <div className="text-sm text-yellow-700 mt-2">Loading...</div>
      </div>
    )
  }

  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
      <h3 className="font-bold text-yellow-800">Debug Info</h3>
      <div className="text-sm text-yellow-700 mt-2">
        <div>ENABLE_MOCK_LOGIN: {process.env.NEXT_PUBLIC_ENABLE_MOCK_LOGIN || 'undefined'}</div>
        <div>ENABLE_MOCK_FALLBACK: {process.env.NEXT_PUBLIC_ENABLE_MOCK_FALLBACK || 'undefined'}</div>
        <div>NEXTAUTH_URL: {process.env.NEXTAUTH_URL || 'Not set'}</div>
      </div>
    </div>
  )
}
