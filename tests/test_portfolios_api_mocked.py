"""
Test cases for portfolio API endpoints with mocked dependencies.
"""

import pytest
import json
from datetime import datetime, timezone
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock

from src.functions.api.portfolios import handler
from src.shared.models.portfolio import (
    Portfolio,
    PortfolioType,
    PortfolioStatus,
    PortfolioCreateRequest,
    PortfolioUpdateRequest,
    AddHoldingRequest,
    TransactionType,
)
from src.shared.models.fund import Currency, RiskLevel


@pytest.fixture
def mock_portfolio():
    """Create a mock portfolio for testing."""
    return Portfolio(
        portfolio_id="portfolio-test-123",
        name="Test Portfolio",
        description="A test portfolio for unit testing",
        portfolio_type=PortfolioType.PERSONAL,
        status=PortfolioStatus.ACTIVE,
        user_id="user-test-123",
        base_currency=Currency.USD,
        inception_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
        total_value=Decimal("10000.00"),
        total_cost_basis=Decimal("9500.00"),
        cash_balance=Decimal("500.00"),
        total_gain_loss=Decimal("500.00"),
        total_gain_loss_pct=Decimal("5.26"),
        holdings=[],
        recent_transactions=[],
        risk_level=RiskLevel.MODERATE,
        benchmark="S&P 500",
        tags=["test", "personal"],
        custom_fields={"advisor": "Test Advisor"},
        created_at=datetime(2023, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2023, 1, 2, tzinfo=timezone.utc),
    )


@pytest.fixture
def mock_lambda_context():
    """Create a mock Lambda context."""
    context = Mock()
    context.function_name = "test-portfolio-function"
    context.function_version = "1"
    context.invoked_function_arn = "arn:aws:lambda:us-east-1:123456789012:function:test"
    context.memory_limit_in_mb = 128
    context.remaining_time_in_millis = lambda: 30000
    context.aws_request_id = "test-request-id"
    return context


@pytest.fixture
def valid_session_result():
    """Mock valid session validation result."""
    return {
        "valid": True,
        "user_info": {
            "user_id": "user-test-123",
            "email": "<EMAIL>",
            "name": "Test User",
        },
    }


class TestPortfolioAPIEndpoints:
    """Test portfolio API endpoints."""

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_list_portfolios_success(
        self, mock_get_repo, mock_session_manager, mock_portfolio, valid_session_result, mock_lambda_context
    ):
        """Test successful portfolio listing."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        mock_repo = Mock()
        mock_repo.list_portfolios.return_value = {
            "portfolios": [mock_portfolio],
            "count": 1,
            "has_more": False,
        }
        mock_get_repo.return_value = mock_repo

        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/portfolios",
            "queryStringParameters": {"page_size": "20"},
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert "portfolios" in body["data"]
        assert len(body["data"]["portfolios"]) == 1

        # Verify repository was called correctly
        mock_repo.list_portfolios.assert_called_once_with(
            user_id="user-test-123",
            limit=20,
            status=None,
            portfolio_type=None,
            search=None,
        )

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_get_portfolio_by_id_success(
        self, mock_get_repo, mock_session_manager, mock_portfolio, valid_session_result, mock_lambda_context
    ):
        """Test successful portfolio retrieval by ID."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        mock_repo = Mock()
        mock_repo.get_portfolio.return_value = mock_portfolio
        mock_get_repo.return_value = mock_repo

        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/portfolios/portfolio-test-123",
            "pathParameters": {"portfolio_id": "portfolio-test-123"},
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["data"]["portfolio_id"] == "portfolio-test-123"

        # Verify repository was called correctly
        mock_repo.get_portfolio.assert_called_once_with("portfolio-test-123")

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_get_portfolio_not_found(
        self, mock_get_repo, mock_session_manager, valid_session_result, mock_lambda_context
    ):
        """Test portfolio not found scenario."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        mock_repo = Mock()
        mock_repo.get_portfolio.return_value = None
        mock_get_repo.return_value = mock_repo

        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/portfolios/nonexistent",
            "pathParameters": {"portfolio_id": "nonexistent"},
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 404
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "not found" in body["message"].lower()

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_create_portfolio_success(
        self, mock_get_repo, mock_session_manager, mock_portfolio, valid_session_result, mock_lambda_context
    ):
        """Test successful portfolio creation."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        mock_repo = Mock()
        mock_repo.create_portfolio.return_value = mock_portfolio
        mock_get_repo.return_value = mock_repo

        # Create test event
        portfolio_data = {
            "name": "New Test Portfolio",
            "description": "A new portfolio for testing",
            "portfolio_type": "personal",
            "base_currency": "USD",
            "cash_balance": 1000.0,
            "risk_level": "moderate",
            "benchmark": "S&P 500",
            "tags": ["test", "new"],
        }

        event = {
            "httpMethod": "POST",
            "path": "/portfolios",
            "body": json.dumps(portfolio_data),
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 201
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["data"]["portfolio_id"] == "portfolio-test-123"

        # Verify repository was called correctly
        mock_repo.create_portfolio.assert_called_once()
        call_args = mock_repo.create_portfolio.call_args
        assert call_args[0][1] == "user-test-123"  # user_id
        assert isinstance(call_args[0][0], PortfolioCreateRequest)

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_update_portfolio_success(
        self, mock_get_repo, mock_session_manager, mock_portfolio, valid_session_result, mock_lambda_context
    ):
        """Test successful portfolio update."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        updated_portfolio = mock_portfolio.model_copy()
        updated_portfolio.name = "Updated Portfolio Name"

        mock_repo = Mock()
        mock_repo.get_portfolio.return_value = mock_portfolio
        mock_repo.update_portfolio.return_value = updated_portfolio
        mock_get_repo.return_value = mock_repo

        # Create test event
        update_data = {
            "name": "Updated Portfolio Name",
            "status": "inactive",
        }

        event = {
            "httpMethod": "PUT",
            "path": "/portfolios/portfolio-test-123",
            "pathParameters": {"portfolio_id": "portfolio-test-123"},
            "body": json.dumps(update_data),
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["data"]["name"] == "Updated Portfolio Name"

        # Verify repository was called correctly
        mock_repo.update_portfolio.assert_called_once()

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_delete_portfolio_success(
        self, mock_get_repo, mock_session_manager, mock_portfolio, valid_session_result, mock_lambda_context
    ):
        """Test successful portfolio deletion."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        mock_repo = Mock()
        mock_repo.get_portfolio.return_value = mock_portfolio
        mock_repo.delete_portfolio.return_value = True
        mock_get_repo.return_value = mock_repo

        # Create test event
        event = {
            "httpMethod": "DELETE",
            "path": "/portfolios/portfolio-test-123",
            "pathParameters": {"portfolio_id": "portfolio-test-123"},
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 200
        body = json.loads(response["body"])
        assert body["success"] is True
        assert body["data"]["deleted"] is True

        # Verify repository was called correctly
        mock_repo.delete_portfolio.assert_called_once_with("portfolio-test-123")

    @patch("src.functions.api.portfolios.SessionManager")
    def test_unauthorized_access(self, mock_session_manager, mock_lambda_context):
        """Test unauthorized access to portfolio endpoints."""
        # Setup mock for invalid session
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = {"valid": False}
        mock_session_manager.return_value = mock_session_instance

        # Create test event
        event = {
            "httpMethod": "GET",
            "path": "/portfolios",
            "headers": {},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 401
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "session" in body["message"].lower()

    def test_invalid_http_method(self, mock_lambda_context):
        """Test invalid HTTP method."""
        # Create test event with unsupported method
        event = {
            "httpMethod": "PATCH",
            "path": "/portfolios",
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 405
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "not allowed" in body["message"].lower()

    def test_invalid_path(self, mock_lambda_context):
        """Test invalid path."""
        # Create test event with unsupported path
        event = {
            "httpMethod": "GET",
            "path": "/invalid-path",
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 404
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "not found" in body["message"].lower()

    @patch("src.functions.api.portfolios.SessionManager")
    @patch("src.functions.api.portfolios.get_portfolio_repository")
    def test_create_portfolio_validation_error(
        self, mock_get_repo, mock_session_manager, valid_session_result, mock_lambda_context
    ):
        """Test portfolio creation with validation errors."""
        # Setup mocks
        mock_session_instance = Mock()
        mock_session_instance.validate_session.return_value = valid_session_result
        mock_session_manager.return_value = mock_session_instance

        # Create test event with invalid data
        invalid_data = {
            "name": "",  # Empty name should fail validation
            "portfolio_type": "invalid_type",
        }

        event = {
            "httpMethod": "POST",
            "path": "/portfolios",
            "body": json.dumps(invalid_data),
            "headers": {"Authorization": "Bearer test-token"},
        }

        # Call handler
        response = handler(event, mock_lambda_context)

        # Verify response
        assert response["statusCode"] == 400
        body = json.loads(response["body"])
        assert body["success"] is False
        assert "validation" in body["message"].lower() or "invalid" in body["message"].lower()
