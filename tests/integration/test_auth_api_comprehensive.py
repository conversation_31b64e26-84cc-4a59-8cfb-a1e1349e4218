#!/usr/bin/env python3
"""
Comprehensive Auth API Integration Tests
Tests the authentication endpoints as the frontend would interact with them.
"""

import sys
import os
import json
import requests
from typing import Dict, Any, Optional
import time

# Add the src directory to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, os.path.join(project_root, "src"))


class AuthAPITester:
    """Comprehensive tester for Auth API endpoints"""

    def __init__(self):
        self.api_base_url = (
            "https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev"
        )
        self.test_results = []
        self.auth_token = None
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "TestPassword123!"

    def log_test_result(
        self, test_name: str, success: bool, message: str, response_data: Dict = None
    ):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "response_data": response_data,
        }
        self.test_results.append(result)

        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}: {message}")
        if response_data:
            print(f"   Response: {json.dumps(response_data, indent=2)[:200]}...")
        print()

    def test_health_check(self):
        """Test basic health check endpoint"""
        try:
            response = requests.get(f"{self.api_base_url}/auth/health")

            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Auth Health Check",
                    True,
                    f"API is healthy. Status: {response.status_code}",
                    data,
                )
                return True
            else:
                self.log_test_result(
                    "Auth Health Check",
                    False,
                    f"Unexpected status code: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Health Check",
                False,
                f"Request failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_login_endpoint(self):
        """Test login endpoint with test credentials"""
        try:
            # Try to authenticate directly with Cognito first
            import boto3

            cognito_client = boto3.client("cognito-idp", region_name="ap-northeast-1")
            user_pool_id = "ap-northeast-1_H2kKHGUAT"
            client_id = "2jh76f894g6lv9vrus4qbb9hu7"

            # Try different auth flows
            auth_flows = ["ADMIN_USER_PASSWORD_AUTH", "USER_PASSWORD_AUTH"]

            for auth_flow in auth_flows:
                try:
                    if auth_flow.startswith("ADMIN_"):
                        response = cognito_client.admin_initiate_auth(
                            UserPoolId=user_pool_id,
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )
                    else:
                        response = cognito_client.initiate_auth(
                            ClientId=client_id,
                            AuthFlow=auth_flow,
                            AuthParameters={
                                "USERNAME": self.test_user_email,
                                "PASSWORD": self.test_user_password,
                            },
                        )

                    if "AuthenticationResult" in response:
                        auth_result = response["AuthenticationResult"]
                        self.auth_token = auth_result["AccessToken"]
                        self.log_test_result(
                            "Auth Login",
                            True,
                            f"Cognito authentication successful using {auth_flow}",
                            {
                                "auth_flow": auth_flow,
                                "has_access_token": True,
                                "token_length": len(self.auth_token),
                            },
                        )
                        return True

                except Exception as flow_error:
                    continue

            # If Cognito auth failed, try the API endpoint
            login_data = {
                "email": self.test_user_email,
                "password": self.test_user_password,
            }

            response = requests.post(
                f"{self.api_base_url}/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            if response.status_code == 200:
                data = response.json()
                if "access_token" in data:
                    self.auth_token = data["access_token"]
                    self.log_test_result(
                        "Auth Login",
                        True,
                        "API login successful, received access token",
                        {
                            "has_access_token": True,
                            "token_length": len(self.auth_token),
                        },
                    )
                    return True
                else:
                    self.log_test_result(
                        "Auth Login", False, "Login response missing access_token", data
                    )
                    return False
            else:
                self.log_test_result(
                    "Auth Login",
                    False,
                    f"Both Cognito and API login failed. API status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Login",
                False,
                f"Login request failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_user_profile_endpoint(self):
        """Test user profile endpoint with authentication"""
        if not self.auth_token:
            self.log_test_result(
                "Auth User Profile", False, "No auth token available, skipping test", {}
            )
            return False

        try:
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
            }

            response = requests.get(
                f"{self.api_base_url}/auth/profile", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Auth User Profile", True, "Profile retrieved successfully", data
                )
                return True
            else:
                self.log_test_result(
                    "Auth User Profile",
                    False,
                    f"Profile request failed with status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth User Profile",
                False,
                f"Profile request failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_token_validation(self):
        """Test token validation endpoint"""
        if not self.auth_token:
            self.log_test_result(
                "Auth Token Validation",
                False,
                "No auth token available, skipping test",
                {},
            )
            return False

        try:
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
            }

            response = requests.post(
                f"{self.api_base_url}/auth/validate", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                self.log_test_result(
                    "Auth Token Validation", True, "Token validation successful", data
                )
                return True
            else:
                self.log_test_result(
                    "Auth Token Validation",
                    False,
                    f"Token validation failed with status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Token Validation",
                False,
                f"Token validation request failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_logout_endpoint(self):
        """Test logout endpoint"""
        if not self.auth_token:
            self.log_test_result(
                "Auth Logout", False, "No auth token available, skipping test", {}
            )
            return False

        try:
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
            }

            response = requests.post(
                f"{self.api_base_url}/auth/logout", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                self.log_test_result("Auth Logout", True, "Logout successful", data)
                return True
            else:
                self.log_test_result(
                    "Auth Logout",
                    False,
                    f"Logout failed with status: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Logout",
                False,
                f"Logout request failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_invalid_credentials(self):
        """Test login with invalid credentials"""
        try:
            login_data = {"email": "<EMAIL>", "password": "wrongpassword"}

            response = requests.post(
                f"{self.api_base_url}/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"},
            )

            if response.status_code in [401, 403]:
                self.log_test_result(
                    "Auth Invalid Credentials",
                    True,
                    f"Correctly rejected invalid credentials with status: {response.status_code}",
                    {"status_code": response.status_code},
                )
                return True
            else:
                self.log_test_result(
                    "Auth Invalid Credentials",
                    False,
                    f"Unexpected response to invalid credentials: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Invalid Credentials",
                False,
                f"Invalid credentials test failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def test_unauthorized_access(self):
        """Test accessing protected endpoint without token"""
        try:
            response = requests.get(f"{self.api_base_url}/auth/profile")

            if response.status_code in [401, 403]:
                self.log_test_result(
                    "Auth Unauthorized Access",
                    True,
                    f"Correctly rejected unauthorized access with status: {response.status_code}",
                    {"status_code": response.status_code},
                )
                return True
            else:
                self.log_test_result(
                    "Auth Unauthorized Access",
                    False,
                    f"Unexpected response to unauthorized access: {response.status_code}",
                    {"status_code": response.status_code, "response": response.text},
                )
                return False

        except Exception as e:
            self.log_test_result(
                "Auth Unauthorized Access",
                False,
                f"Unauthorized access test failed: {str(e)}",
                {"error": str(e)},
            )
            return False

    def run_all_tests(self):
        """Run all auth API tests"""
        print("🔐 Starting Comprehensive Auth API Tests")
        print("=" * 60)

        # Test sequence that mimics frontend flow
        tests = [
            self.test_health_check,
            self.test_login_endpoint,
            self.test_user_profile_endpoint,
            self.test_token_validation,
            self.test_logout_endpoint,
            self.test_invalid_credentials,
            self.test_unauthorized_access,
        ]

        passed = 0
        total = len(tests)

        for test in tests:
            if test():
                passed += 1

        # Summary
        print("=" * 60)
        print("📊 AUTH API TEST SUMMARY")
        print("=" * 60)
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {(passed/total)*100:.1f}%")

        # Save detailed results
        results_file = "auth_api_test_results.json"
        with open(results_file, "w") as f:
            json.dump(
                {
                    "summary": {
                        "total_tests": total,
                        "passed": passed,
                        "failed": total - passed,
                        "success_rate": f"{(passed/total)*100:.1f}%",
                        "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                    },
                    "test_results": self.test_results,
                },
                f,
                indent=2,
            )

        print(f"\n📁 Detailed results saved to: {results_file}")

        return passed == total


def main():
    """Main function to run auth API tests"""
    tester = AuthAPITester()
    success = tester.run_all_tests()

    if success:
        print("\n✅ All auth API tests passed!")
        return 0
    else:
        print("\n❌ Some auth API tests failed!")
        return 1


if __name__ == "__main__":
    main()
