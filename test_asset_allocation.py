#!/usr/bin/env python3
"""
Quick test script to check if asset allocation is working correctly
"""

import sys
sys.path.append('src')

from shared.database import get_fund_repository
from functions.api.funds import get_actual_asset_allocation, enrich_fund_with_analytics

def test_asset_allocation():
    print("🧪 Testing Asset Allocation Fix")
    print("=" * 50)
    
    # Get fund from database
    fund_repo = get_fund_repository()
    fund = fund_repo.get_by_fund_id('fund-58b952b6')
    
    if not fund:
        print("❌ Fund not found")
        return
    
    print(f"✅ Found fund: {fund.fund_id}")
    
    # Test the individual function
    print("\n📊 Testing get_actual_asset_allocation():")
    asset_allocation = get_actual_asset_allocation(fund)
    for key, value in asset_allocation.items():
        decimal_places = len(str(value).split('.')[1]) if '.' in str(value) else 0
        print(f"   {key}: {value} ({decimal_places} decimal places)")
    
    # Test the full enrichment
    print("\n🔧 Testing full fund enrichment:")
    enriched_fund = enrich_fund_with_analytics(fund)
    
    analytics_allocation = enriched_fund.get('analytics', {}).get('assetAllocation', {})
    print(f"   Analytics asset allocation: {analytics_allocation}")
    
    # Check if it would trigger mock detection
    print("\n🕵️ Mock Detection Check:")
    for key, value in analytics_allocation.items():
        value_str = str(value)
        if '.' in value_str:
            decimal_places = len(value_str.split('.')[1])
            is_mock_like = decimal_places > 4
            print(f"   {key}: {decimal_places} decimal places - {'🔴 Mock-like' if is_mock_like else '✅ Not mock-like'}")
        else:
            print(f"   {key}: No decimal places - ✅ Not mock-like")

if __name__ == "__main__":
    test_asset_allocation()