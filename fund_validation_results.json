{"summary": {"total_funds": 20, "valid_funds": 0, "invalid_funds": 20, "funds_with_warnings": 0, "validation_date": "2024-06-19"}, "valid_funds": [], "invalid_funds": [{"fund_id": "fund-1895f105", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('11.9')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "ICICI Prudential Mid Cap Fund 7", "fund_type": "etf", "status": "active"}, {"fund_id": "fund-f1d8c755", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('12.6')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Kotak LALA Sectoral Fund 11", "fund_type": "etf", "status": "active"}, {"fund_id": "fund-ee015a40", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('12.4')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Axis Multi Cap Fund 40", "fund_type": "mixed", "status": "active"}, {"fund_id": "fund-025fd2c8", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('22.8')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Aditya Birla Multi Cap Fund 18", "fund_type": "index", "status": "active"}, {"fund_id": "fund-a8e55128", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('27.4')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "ICICI Prudential Small Cap Fund 11", "fund_type": "etf", "status": "active"}, {"fund_id": "fund-f1ce1571", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('21.1')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Aditya Birla Sectoral Fund 29", "fund_type": "bond", "status": "active"}, {"fund_id": "fund-3ae40462", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('14.5')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Axis Small Cap Fund 46", "fund_type": "index", "status": "active"}, {"fund_id": "fund-57bccb91", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...ational': Decimal('6')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Aditya Birla International Fund 31", "fund_type": "mixed", "status": "active"}, {"fund_id": "fund-7b75dc6e", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('24.1')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Aditya Birla Sectoral Fund 37", "fund_type": "mixed", "status": "active"}, {"fund_id": "fund-cdf39c1d", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...ional': Decimal('8.1')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Axis International Fund 16", "fund_type": "equity", "status": "active"}, {"fund_id": "fund-1c06b30b", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...ional': Decimal('8.8')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "UTI Multi Cap Fund 6", "fund_type": "money_market", "status": "active"}, {"fund_id": "fund-54fe7405", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('15.3')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "HDFC Small Cap Fund 39", "fund_type": "money_market", "status": "active"}, {"fund_id": "fund-a17d385c", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('14.5')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Reliance Multi Cap Fund 27", "fund_type": "money_market", "status": "active"}, {"fund_id": "fund-b53acc22", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('20.5')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Axis Sectoral Fund 14", "fund_type": "bond", "status": "active"}, {"fund_id": "fund-735a515f", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('22.6')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "UTI Multi Cap Fund 6", "fund_type": "etf", "status": "active"}, {"fund_id": "fund-ed5b34f7", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...ional': Decimal('7.7')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "UTI Multi Cap Fund 14", "fund_type": "index", "status": "active"}, {"fund_id": "fund-278f25c5", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('16.7')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "ICICI Prudential Sectoral Fund 47", "fund_type": "mixed", "status": "active"}, {"fund_id": "fund-d7099ffb", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...ional': Decimal('9.5')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Kotak Small Cap Fund 1", "fund_type": "equity", "status": "active"}, {"fund_id": "fund-bc7b0392", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('18.2')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Reliance Mid Cap Fund 3", "fund_type": "bond", "status": "active"}, {"fund_id": "fund-7bc09bae", "valid": false, "errors": ["1 validation error for Fund\nholdings\n  Value error, Sector allocation percentages should sum to approximately 100% [type=value_error, input_value={'top_holdings': [{'name'...onal': Decimal('29.3')}}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/value_error"], "warnings": [], "data_issues": [], "fund_name": "Kotak International Fund 16", "fund_type": "equity", "status": "active"}], "funds_with_warnings": []}