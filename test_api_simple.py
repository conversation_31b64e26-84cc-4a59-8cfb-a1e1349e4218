#!/usr/bin/env python3
"""
Simple test to check if the API returns correct asset allocation
"""

import requests
import json

def test_api():
    print("🌐 Testing API Asset Allocation")
    print("=" * 40)
    
    # Test the enriched fund details endpoint
    fund_id = "fund-58b952b6"
    url = f"https://b5uqilw5yk.execute-api.ap-northeast-1.amazonaws.com/dev/funds/{fund_id}/details"
    
    print(f"📡 Calling: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 401:
            print("❌ Authentication required - this is expected")
            print("   The API is working correctly and requires authentication")
            return
        
        if response.status_code == 200:
            data = response.json()
            
            # Check if we have analytics data
            if 'data' in data and 'analytics' in data['data']:
                asset_allocation = data['data']['analytics'].get('assetAllocation', {})
                print("✅ Found asset allocation in response:")
                for key, value in asset_allocation.items():
                    print(f"   {key}: {value}")
                    
                # Check decimal places
                print("\n🔍 Decimal places check:")
                for key, value in asset_allocation.items():
                    if isinstance(value, (int, float)):
                        value_str = str(value)
                        if '.' in value_str:
                            decimal_places = len(value_str.split('.')[1])
                            print(f"   {key}: {decimal_places} decimal places")
                        else:
                            print(f"   {key}: 0 decimal places")
            else:
                print("❌ No analytics.assetAllocation found in response")
                print("Response structure:", list(data.keys()) if data else "No data")
        else:
            print(f"❌ API returned status {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error calling API: {e}")

if __name__ == "__main__":
    test_api()