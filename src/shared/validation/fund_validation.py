"""
Enhanced Fund Validation Service for Server-Side Validation.
Provides comprehensive business rule validation, cross-field validation,
and security checks for fund editing operations.
"""

import re
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum

from pydantic import ValidationError, field_serializer, ConfigDict
from aws_lambda_powertools import Logger

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from shared.validators import (
    CommonValidators,
    BusinessLogicValidators,
    ValidationError as CustomValidationError,
)

from ..models.fund import (
    Fund,
    FundType,
    FundStatus,
    RiskLevel,
    Currency,
    PerformanceMetrics,
    Holdings,
)
from ..models.requests import FundCreateRequest, FundUpdateRequest

logger = Logger()


class ValidationSeverity(str, Enum):
    """Validation error severity levels."""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationResult:
    """Validation result container."""

    def __init__(self):
        self.is_valid = True
        self.errors: List[Dict[str, Any]] = []
        self.warnings: List[Dict[str, Any]] = []
        self.info: List[Dict[str, Any]] = []

    def add_error(self, field: str, message: str, code: str = None):
        """Add validation error."""
        self.is_valid = False
        self.errors.append(
            {
                "field": field,
                "message": message,
                "code": code or "VALIDATION_ERROR",
                "severity": ValidationSeverity.ERROR,
            }
        )

    def add_warning(self, field: str, message: str, code: str = None):
        """Add validation warning."""
        self.warnings.append(
            {
                "field": field,
                "message": message,
                "code": code or "VALIDATION_WARNING",
                "severity": ValidationSeverity.WARNING,
            }
        )

    def add_info(self, field: str, message: str, code: str = None):
        """Add validation info."""
        self.info.append(
            {
                "field": field,
                "message": message,
                "code": code or "VALIDATION_INFO",
                "severity": ValidationSeverity.INFO,
            }
        )

    def get_all_issues(self) -> List[Dict[str, Any]]:
        """Get all validation issues."""
        return self.errors + self.warnings + self.info


class FundValidationService:
    """Enhanced fund validation service with comprehensive business rules."""

    def __init__(self):
        self.logger = logger

    def validate_fund_creation(
        self, request: FundCreateRequest, user_context: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate fund creation request with comprehensive business rules.

        Args:
            request: Fund creation request
            user_context: User context for permission validation

        Returns:
            ValidationResult with detailed validation feedback
        """
        result = ValidationResult()

        # Security validation
        self._validate_user_permissions(user_context, "create_fund", result)

        # Business rule validation
        self._validate_fund_id_uniqueness(request.fund_id, result)
        self._validate_fund_basic_info(request, result)
        self._validate_financial_data(request, result)
        self._validate_management_info(request, result)
        self._validate_classification_codes(request, result)
        self._validate_cross_field_consistency(request, result)
        self._validate_performance_metrics(
            request.performance_metrics, request.fund_type, result
        )
        self._validate_holdings_data(request.holdings, result)

        return result

    def validate_fund_update(
        self,
        request: FundUpdateRequest,
        existing_fund: Fund,
        user_context: Dict[str, Any],
    ) -> ValidationResult:
        """
        Validate fund update request with enhanced business rules.

        Args:
            request: Fund update request
            existing_fund: Current fund data
            user_context: User context for permission validation

        Returns:
            ValidationResult with detailed validation feedback
        """
        result = ValidationResult()

        # Security validation
        self._validate_user_permissions(user_context, "update_fund", result)
        self._validate_fund_edit_permissions(existing_fund, user_context, result)

        # Business rule validation for updates
        self._validate_status_transitions(request.status, existing_fund.status, result)
        self._validate_immutable_fields(request, existing_fund, result)
        self._validate_update_financial_data(request, existing_fund, result)

        # Create combined data for cross-field validation
        combined_data = self._merge_update_data(request, existing_fund)
        self._validate_cross_field_consistency(combined_data, result)

        if request.performance_metrics:
            self._validate_performance_metrics(
                request.performance_metrics,
                request.fund_type or existing_fund.fund_type,
                result,
            )

        if request.holdings:
            self._validate_holdings_data(request.holdings, result)

        return result

    def validate_bulk_fund_updates(
        self, updates: List[Dict[str, Any]], user_context: Dict[str, Any]
    ) -> Dict[str, ValidationResult]:
        """
        Validate bulk fund update operations.

        Args:
            updates: List of fund update operations
            user_context: User context for permission validation

        Returns:
            Dictionary mapping fund_id to ValidationResult
        """
        results = {}

        for update_data in updates:
            fund_id = update_data.get("fund_id")
            if not fund_id:
                result = ValidationResult()
                result.add_error("fund_id", "Fund ID is required for bulk updates")
                results[f"unknown_{len(results)}"] = result
                continue

            # Individual validation would require existing fund data
            # This is a placeholder for bulk-specific validation
            result = ValidationResult()
            self._validate_bulk_operation_limits(update_data, result)
            results[fund_id] = result

        return results

    def _validate_user_permissions(
        self, user_context: Dict[str, Any], operation: str, result: ValidationResult
    ):
        """Validate user has required permissions for the operation."""
        user_role = user_context.get("role", "")
        user_id = user_context.get("user_id", "")

        if not user_id:
            result.add_error("authentication", "User authentication required")
            return

        # Define role-based permissions
        required_permissions = {
            "create_fund": ["ADMIN", "FUND_MANAGER", "SENIOR_ANALYST"],
            "update_fund": ["ADMIN", "FUND_MANAGER", "SENIOR_ANALYST"],
            "delete_fund": ["ADMIN", "FUND_MANAGER"],
        }

        allowed_roles = required_permissions.get(operation, [])
        if user_role not in allowed_roles:
            result.add_error(
                "authorization",
                f"Insufficient permissions for {operation}. Required roles: {', '.join(allowed_roles)}",
            )

    def _validate_fund_id_uniqueness(self, fund_id: str, result: ValidationResult):
        """Validate fund ID is unique (placeholder - would check database)."""
        # In real implementation, this would query the database
        # For now, just validate format
        try:
            validate_id_format(fund_id, "Fund ID")
        except ValueError as e:
            result.add_error("fund_id", str(e))

    def _validate_fund_basic_info(
        self, request: FundCreateRequest, result: ValidationResult
    ):
        """Validate basic fund information."""
        # Name validation
        if not request.name or len(request.name.strip()) < 3:
            result.add_error("name", "Fund name must be at least 3 characters long")

        if len(request.name) > 200:
            result.add_error("name", "Fund name cannot exceed 200 characters")

        # Fund type and risk level consistency
        if request.risk_level:
            self._validate_fund_type_risk_consistency(
                request.fund_type, request.risk_level, result
            )

        # Description length check
        if request.description and len(request.description) > 1000:
            result.add_error("description", "Description cannot exceed 1000 characters")

    def _validate_financial_data(
        self, request: FundCreateRequest, result: ValidationResult
    ):
        """Validate financial data consistency and business rules."""
        # NAV validation
        if request.nav is not None:
            if request.nav <= 0:
                result.add_error("nav", "NAV must be greater than zero")
            elif request.nav > Decimal("10000"):
                result.add_warning(
                    "nav", "NAV seems unusually high, please verify", "HIGH_NAV"
                )

        # Total assets validation
        if request.total_assets is not None:
            if request.total_assets < 0:
                result.add_error("total_assets", "Total assets cannot be negative")

            # Check NAV vs total assets relationship
            if (
                request.nav
                and request.total_assets
                and request.nav > request.total_assets
            ):
                result.add_warning(
                    "nav",
                    "NAV is higher than total assets, which is unusual for most fund types",
                    "NAV_ASSETS_MISMATCH",
                )

        # Expense ratio validation
        if request.expense_ratio is not None:
            if request.expense_ratio < 0:
                result.add_error("expense_ratio", "Expense ratio cannot be negative")
            elif request.expense_ratio > 5:
                result.add_warning(
                    "expense_ratio",
                    "Expense ratio above 5% is quite high",
                    "HIGH_EXPENSE_RATIO",
                )

        # Minimum investment validation
        if request.minimum_investment is not None:
            if request.minimum_investment < 0:
                result.add_error(
                    "minimum_investment", "Minimum investment cannot be negative"
                )
            elif request.minimum_investment > Decimal("10000000"):
                result.add_warning(
                    "minimum_investment",
                    "Very high minimum investment requirement",
                    "HIGH_MINIMUM",
                )

    def _validate_management_info(
        self, request: FundCreateRequest, result: ValidationResult
    ):
        """Validate management information."""
        if request.fund_manager:
            if len(request.fund_manager.strip()) < 2:
                result.add_error(
                    "fund_manager", "Fund manager name must be at least 2 characters"
                )
            elif not re.match(r"^[\w\s\.-]+$", request.fund_manager):
                result.add_error(
                    "fund_manager", "Fund manager name contains invalid characters"
                )

        if request.management_company:
            if len(request.management_company.strip()) < 2:
                result.add_error(
                    "management_company",
                    "Management company name must be at least 2 characters",
                )

    def _validate_classification_codes(
        self, request: FundCreateRequest, result: ValidationResult
    ):
        """Validate ISIN, CUSIP, and other classification codes."""
        # ISIN validation (enhanced)
        if request.isin:
            if not re.match(r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$", request.isin):
                result.add_error(
                    "isin",
                    "ISIN must be in format: 2 letters + 9 alphanumeric + 1 digit",
                )
            else:
                # ISIN check digit validation
                if not self._validate_isin_check_digit(request.isin):
                    result.add_error("isin", "ISIN check digit is invalid")

        # CUSIP validation (enhanced)
        if request.cusip:
            if not re.match(r"^[0-9A-Z]{8}[0-9]$", request.cusip):
                result.add_error(
                    "cusip",
                    "CUSIP must be 8 alphanumeric characters followed by 1 digit",
                )
            else:
                # CUSIP check digit validation
                if not self._validate_cusip_check_digit(request.cusip):
                    result.add_error("cusip", "CUSIP check digit is invalid")

        # Bloomberg ticker validation
        if request.bloomberg_ticker:
            if not re.match(r"^[A-Z0-9\.\-\s:]+$", request.bloomberg_ticker):
                result.add_error(
                    "bloomberg_ticker",
                    "Bloomberg ticker should contain only uppercase letters, numbers, dots, hyphens, spaces, and colons",
                )

    def _validate_cross_field_consistency(self, data: Any, result: ValidationResult):
        """Validate consistency across multiple fields."""
        # Fund type and currency consistency
        fund_type = getattr(data, "fund_type", None)
        currency = getattr(data, "currency", None)

        if fund_type == FundType.MONEY_MARKET and currency != Currency.USD:
            result.add_warning(
                "currency",
                "Money market funds typically use USD as base currency",
                "CURRENCY_TYPE_MISMATCH",
            )

        # Inception date validation
        inception_date = getattr(data, "inception_date", None)
        if inception_date:
            if inception_date > datetime.now(timezone.utc):
                result.add_error(
                    "inception_date", "Inception date cannot be in the future"
                )
            elif inception_date < datetime(1900, 1, 1):
                result.add_warning(
                    "inception_date",
                    "Inception date seems unusually old",
                    "OLD_INCEPTION_DATE",
                )

    def _validate_performance_metrics(
        self,
        metrics: Optional[PerformanceMetrics],
        fund_type: FundType,
        result: ValidationResult,
    ):
        """Validate performance metrics consistency."""
        if not metrics:
            return

        # Return consistency validation
        returns = [
            ("ytd_return", metrics.ytd_return),
            ("one_year_return", metrics.one_year_return),
            ("three_year_return", metrics.three_year_return),
            ("five_year_return", metrics.five_year_return),
            ("inception_return", metrics.inception_return),
        ]

        for field_name, return_value in returns:
            if return_value is not None:
                if return_value < -100:
                    result.add_error(field_name, "Return cannot be less than -100%")
                elif return_value > 1000:
                    result.add_warning(
                        field_name, "Return above 1000% seems unusual", "HIGH_RETURN"
                    )

        # Volatility validation
        if metrics.volatility is not None:
            if metrics.volatility < 0:
                result.add_error("volatility", "Volatility cannot be negative")
            elif metrics.volatility > 100:
                result.add_warning(
                    "volatility",
                    "Volatility above 100% is extremely high",
                    "HIGH_VOLATILITY",
                )

        # Sharpe ratio validation
        if metrics.sharpe_ratio is not None:
            if metrics.sharpe_ratio < -5:
                result.add_warning(
                    "sharpe_ratio",
                    "Sharpe ratio below -5 indicates very poor risk-adjusted performance",
                )
            elif metrics.sharpe_ratio > 5:
                result.add_warning(
                    "sharpe_ratio", "Sharpe ratio above 5 is exceptionally high"
                )

    def _validate_holdings_data(
        self, holdings: Optional[Holdings], result: ValidationResult
    ):
        """Validate holdings allocation data."""
        if not holdings:
            return

        # Sector allocation validation
        if holdings.sector_allocation:
            total_allocation = sum(holdings.sector_allocation.values())
            if abs(total_allocation - Decimal("100")) > Decimal("5"):
                result.add_warning(
                    "sector_allocation",
                    f"Sector allocations should sum to approximately 100% (currently {total_allocation}%)",
                )

        # Geographic allocation validation
        if holdings.geographic_allocation:
            total_allocation = sum(holdings.geographic_allocation.values())
            if abs(total_allocation - Decimal("100")) > Decimal("5"):
                result.add_warning(
                    "geographic_allocation",
                    f"Geographic allocations should sum to approximately 100% (currently {total_allocation}%)",
                )

    def _validate_status_transitions(
        self,
        new_status: Optional[FundStatus],
        current_status: FundStatus,
        result: ValidationResult,
    ):
        """Validate allowed fund status transitions."""
        if not new_status or new_status == current_status:
            return

        # Define valid status transitions
        valid_transitions = {
            FundStatus.ACTIVE: [
                FundStatus.INACTIVE,
                FundStatus.SUSPENDED,
                FundStatus.LIQUIDATING,
            ],
            FundStatus.INACTIVE: [FundStatus.ACTIVE, FundStatus.CLOSED],
            FundStatus.SUSPENDED: [FundStatus.ACTIVE, FundStatus.LIQUIDATING],
            FundStatus.LIQUIDATING: [FundStatus.CLOSED],
            FundStatus.CLOSED: [],  # No transitions allowed from closed
        }

        allowed_statuses = valid_transitions.get(current_status, [])
        if new_status not in allowed_statuses:
            result.add_error(
                "status",
                f"Invalid status transition from {current_status} to {new_status}",
            )

    def _validate_immutable_fields(
        self, request: FundUpdateRequest, existing_fund: Fund, result: ValidationResult
    ):
        """Validate that immutable fields are not being changed."""
        immutable_fields = ["fund_id", "inception_date"]

        for field in immutable_fields:
            if hasattr(request, field):
                new_value = getattr(request, field)
                existing_value = getattr(existing_fund, field)

                if new_value is not None and new_value != existing_value:
                    result.add_error(
                        field, f"{field} cannot be modified after fund creation"
                    )

    def _validate_fund_edit_permissions(
        self, fund: Fund, user_context: Dict[str, Any], result: ValidationResult
    ):
        """Validate user permissions for editing specific fund."""
        user_role = user_context.get("role", "")

        # Additional fund-specific permission checks
        if fund.status == FundStatus.CLOSED:
            if user_role != "ADMIN":
                result.add_error(
                    "fund_status", "Only administrators can modify closed funds"
                )

        if fund.fund_type == FundType.INDEX and user_role not in [
            "ADMIN",
            "SENIOR_ANALYST",
        ]:
            result.add_warning(
                "fund_type", "Index fund modifications require senior approval"
            )

    def _validate_update_financial_data(
        self, request: FundUpdateRequest, existing_fund: Fund, result: ValidationResult
    ):
        """Validate financial data updates with historical context."""
        # NAV change validation
        if request.nav is not None and existing_fund.nav is not None:
            nav_change_percent = abs(
                (request.nav - existing_fund.nav) / existing_fund.nav * 100
            )
            if nav_change_percent > 50:
                result.add_warning(
                    "nav",
                    f"NAV change of {nav_change_percent:.1f}% is significant",
                    "LARGE_NAV_CHANGE",
                )

    def _validate_bulk_operation_limits(
        self, update_data: Dict[str, Any], result: ValidationResult
    ):
        """Validate bulk operation constraints."""
        # Limit number of fields being updated
        if len(update_data) > 20:
            result.add_warning(
                "bulk_operation", "Updating many fields at once may require review"
            )

    def _merge_update_data(
        self, request: FundUpdateRequest, existing_fund: Fund
    ) -> Any:
        """Merge update request with existing fund data for validation."""

        # Create a temporary object with merged data
        class MergedData:
            pass

        merged = MergedData()

        # Copy existing fund data
        for field in ["fund_type", "currency", "inception_date", "nav", "total_assets"]:
            setattr(merged, field, getattr(existing_fund, field))

        # Override with update data
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                setattr(merged, field, value)

        return merged

    def _validate_fund_type_risk_consistency(
        self, fund_type: FundType, risk_level: RiskLevel, result: ValidationResult
    ):
        """Validate consistency between fund type and risk level."""
        risk_mappings = {
            FundType.MONEY_MARKET: [RiskLevel.VERY_LOW],
            FundType.BOND: [RiskLevel.VERY_LOW, RiskLevel.LOW, RiskLevel.MODERATE],
            FundType.EQUITY: [RiskLevel.MODERATE, RiskLevel.HIGH, RiskLevel.VERY_HIGH],
            FundType.ALTERNATIVE: [RiskLevel.HIGH, RiskLevel.VERY_HIGH],
            FundType.MIXED: [RiskLevel.LOW, RiskLevel.MODERATE, RiskLevel.HIGH],
        }

        expected_risks = risk_mappings.get(fund_type, [])
        if expected_risks and risk_level not in expected_risks:
            result.add_warning(
                "risk_level",
                f"Risk level {risk_level} is unusual for {fund_type} funds",
                "RISK_TYPE_MISMATCH",
            )

    def _validate_isin_check_digit(self, isin: str) -> bool:
        """Validate ISIN check digit using Luhn algorithm."""
        # Simplified ISIN validation - real implementation would be more comprehensive
        try:
            # Convert letters to numbers (A=10, B=11, etc.)
            numeric_string = ""
            for char in isin[:-1]:  # Exclude check digit
                if char.isalpha():
                    numeric_string += str(ord(char) - ord("A") + 10)
                else:
                    numeric_string += char

            # Apply Luhn algorithm
            total = 0
            for i, digit in enumerate(reversed(numeric_string)):
                n = int(digit)
                if i % 2 == 1:  # Every second digit from right
                    n *= 2
                    if n > 9:
                        n = n // 10 + n % 10
                total += n

            check_digit = (10 - (total % 10)) % 10
            return check_digit == int(isin[-1])

        except (ValueError, IndexError):
            return False

    def _validate_cusip_check_digit(self, cusip: str) -> bool:
        """Validate CUSIP check digit."""
        # Simplified CUSIP validation
        try:
            total = 0
            for i, char in enumerate(cusip[:-1]):  # Exclude check digit
                if char.isdigit():
                    value = int(char)
                else:
                    value = ord(char) - ord("A") + 10

                if i % 2 == 1:  # Every second position
                    value *= 2

                total += value // 10 + value % 10

            check_digit = (10 - (total % 10)) % 10
            return check_digit == int(cusip[-1])

        except (ValueError, IndexError):
            return False


# Local validation functions to avoid circular imports
def validate_id_format(
    value: str, field_name: str = "ID", min_length: int = 3, max_length: int = 50
) -> str:
    """Validate ID format (alphanumeric, hyphens, underscores)."""
    if not value:
        raise ValueError(f"{field_name} is required")
    if len(value) < min_length:
        raise ValueError(f"{field_name} must be at least {min_length} characters")
    if len(value) > max_length:
        raise ValueError(f"{field_name} must not exceed {max_length} characters")
    if not re.match(r"^[a-zA-Z0-9_-]+$", value):
        raise ValueError(
            f"{field_name} can only contain letters, numbers, hyphens, and underscores"
        )
    return value
