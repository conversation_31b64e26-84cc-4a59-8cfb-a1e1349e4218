"""
Session management and security utilities for FundFlow.
Handles JWT token validation, session timeouts, and security checks.
"""

import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
import os
import hashlib
import hmac
from base64 import b64decode, b64encode

import boto3
from aws_lambda_powertools import Logger
from botocore.exceptions import ClientError
import jwt
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError

logger = Logger()


class SessionManager:
    """Manages user sessions and JWT token validation."""

    def __init__(self):
        self.cognito_client = boto3.client("cognito-idp")
        self.user_pool_id = os.environ.get("USER_POOL_ID")
        self.user_pool_client_id = os.environ.get("USER_POOL_CLIENT_ID")
        self.region = os.environ.get("AWS_REGION", "us-east-1")

        # Session timeout configuration (in seconds)
        self.session_timeout = int(
            os.environ.get("SESSION_TIMEOUT_SECONDS", "3600")
        )  # 1 hour default
        self.refresh_threshold = int(
            os.environ.get("REFRESH_THRESHOLD_SECONDS", "900")
        )  # 15 minutes

    def validate_session(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate user session from Lambda event.

        Args:
            event: Lambda event containing authorization context

        Returns:
            Dict containing session validation results

        Raises:
            SessionExpiredError: If session has expired
            InvalidSessionError: If session is invalid
        """
        try:
            # Extract token from authorization header or request context
            access_token = self._extract_access_token(event)
            if not access_token:
                raise InvalidSessionError("No access token found")

            # Validate token with Cognito
            user_info = self._validate_cognito_token(access_token)

            # Check session timeout
            session_info = self._check_session_timeout(user_info)

            # Update last activity timestamp
            session_info["last_activity"] = int(time.time())

            return {
                "valid": True,
                "user_info": user_info,
                "session_info": session_info,
                "requires_refresh": session_info.get("requires_refresh", False),
            }

        except (SessionExpiredError, InvalidSessionError) as e:
            logger.warning(f"Session validation failed: {str(e)}")
            return {"valid": False, "error": str(e), "error_type": type(e).__name__}
        except Exception as e:
            logger.error(f"Unexpected error during session validation: {str(e)}")
            return {
                "valid": False,
                "error": "Internal session validation error",
                "error_type": "InternalError",
            }

    def _extract_access_token(self, event: Dict[str, Any]) -> Optional[str]:
        """Extract access token from Lambda event."""
        # Try to get from Authorization header
        headers = event.get("headers", {})
        auth_header = headers.get("Authorization") or headers.get("authorization")

        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Remove 'Bearer ' prefix

        # Try to get from request context (if processed by authorizer)
        request_context = event.get("requestContext", {})
        authorizer = request_context.get("authorizer", {})

        return authorizer.get("access_token")

    def _validate_cognito_token(self, access_token: str) -> Dict[str, Any]:
        """Validate access token with AWS Cognito."""
        try:
            # Get user info from Cognito using the access token
            response = self.cognito_client.get_user(AccessToken=access_token)

            # Extract user attributes
            user_attributes = {}
            for attr in response.get("UserAttributes", []):
                user_attributes[attr["Name"]] = attr["Value"]

            return {
                "username": response.get("Username"),
                "user_sub": user_attributes.get("sub"),
                "email": user_attributes.get("email"),
                "email_verified": user_attributes.get("email_verified") == "true",
                "user_status": response.get("UserStatus"),
                "attributes": user_attributes,
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]

            if error_code == "NotAuthorizedException":
                raise InvalidSessionError("Invalid or expired access token")
            elif error_code == "UserNotFoundException":
                raise InvalidSessionError("User not found")
            else:
                logger.error(f"Cognito error during token validation: {error_code}")
                raise InvalidSessionError(f"Token validation failed: {error_code}")

    def _check_session_timeout(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """Check if session has timed out."""
        current_time = int(time.time())

        # For now, we'll use the current time as session start
        # In a production system, you'd store session start time in a database
        session_start = current_time
        session_duration = current_time - session_start

        requires_refresh = session_duration > (
            self.session_timeout - self.refresh_threshold
        )

        if session_duration > self.session_timeout:
            raise SessionExpiredError("Session has expired")

        return {
            "session_start": session_start,
            "session_duration": session_duration,
            "time_remaining": self.session_timeout - session_duration,
            "requires_refresh": requires_refresh,
        }

    def invalidate_session(self, access_token: str) -> bool:
        """
        Invalidate a user session by revoking the access token.

        Args:
            access_token: The access token to revoke

        Returns:
            bool: True if successfully invalidated
        """
        try:
            # Revoke the access token in Cognito
            self.cognito_client.global_sign_out(AccessToken=access_token)
            logger.info("Successfully invalidated user session")
            return True

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            logger.error(f"Failed to invalidate session: {error_code}")
            return False


class SecurityEnforcer:
    """Enforces security policies and protections."""

    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.csrf_protection = CSRFProtection()

    def validate_request_security(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive security validation on incoming request.

        Args:
            event: Lambda event

        Returns:
            Dict containing security validation results
        """
        security_checks = {
            "rate_limit_passed": True,
            "csrf_valid": True,
            "input_sanitized": True,
            "security_headers_valid": True,
        }

        try:
            # Rate limiting check
            client_ip = self._get_client_ip(event)
            if not self.rate_limiter.check_rate_limit(
                client_ip, event.get("httpMethod", "")
            ):
                security_checks["rate_limit_passed"] = False
                return security_checks

            # CSRF protection for state-changing operations
            if event.get("httpMethod") in ["POST", "PUT", "DELETE"]:
                if not self.csrf_protection.validate_csrf_token(event):
                    security_checks["csrf_valid"] = False
                    return security_checks

            # Input sanitization
            sanitized_event = self._sanitize_input(event)

            return security_checks

        except Exception as e:
            logger.error(f"Security validation error: {str(e)}")
            return {
                "rate_limit_passed": False,
                "csrf_valid": False,
                "input_sanitized": False,
                "security_headers_valid": False,
                "error": str(e),
            }

    def _get_client_ip(self, event: Dict[str, Any]) -> str:
        """Extract client IP address from Lambda event."""
        # Try various headers for the real IP
        headers = event.get("headers", {})

        # Check common forwarded IP headers
        for header in ["X-Forwarded-For", "X-Real-IP", "X-Client-IP"]:
            ip = headers.get(header) or headers.get(header.lower())
            if ip:
                # X-Forwarded-For can have multiple IPs, take the first one
                return ip.split(",")[0].strip()

        # Fallback to source IP from request context
        request_context = event.get("requestContext", {})
        identity = request_context.get("identity", {})
        return identity.get("sourceIp", "unknown")

    def _sanitize_input(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize input data to prevent injection attacks."""
        if "body" in event and event["body"]:
            try:
                if isinstance(event["body"], str):
                    body = json.loads(event["body"])
                else:
                    body = event["body"]

                # Sanitize string values
                sanitized_body = self._recursive_sanitize(body)
                event["body"] = (
                    json.dumps(sanitized_body)
                    if isinstance(event["body"], str)
                    else sanitized_body
                )

            except json.JSONDecodeError:
                logger.warning("Failed to parse body for sanitization")

        return event

    def _recursive_sanitize(self, data: Any) -> Any:
        """Recursively sanitize data structures."""
        if isinstance(data, dict):
            return {key: self._recursive_sanitize(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._recursive_sanitize(item) for item in data]
        elif isinstance(data, str):
            # Basic XSS prevention
            dangerous_chars = ["<", ">", '"', "'", "&", "javascript:", "script"]
            sanitized = data
            for char in dangerous_chars:
                if char in sanitized.lower():
                    logger.warning(
                        f"Potentially dangerous content detected and sanitized: {char}"
                    )
                    sanitized = sanitized.replace(char, "")
            return sanitized
        else:
            return data


class RateLimiter:
    """Implements rate limiting to prevent abuse."""

    def __init__(self):
        # In a production system, you'd use Redis or DynamoDB for distributed rate limiting
        self.request_counts = {}
        self.window_size = 60  # 1 minute window
        self.max_requests_per_window = {
            "POST": 10,  # 10 POST requests per minute
            "PUT": 10,  # 10 PUT requests per minute
            "DELETE": 5,  # 5 DELETE requests per minute
            "GET": 60,  # 60 GET requests per minute
        }

    def check_rate_limit(self, client_ip: str, method: str) -> bool:
        """
        Check if request is within rate limits.

        Args:
            client_ip: Client IP address
            method: HTTP method

        Returns:
            bool: True if within limits, False if rate limited
        """
        current_time = int(time.time())
        window_start = current_time - self.window_size

        # Clean old entries
        self._cleanup_old_entries(window_start)

        # Count requests in current window
        key = f"{client_ip}:{method}"
        if key not in self.request_counts:
            self.request_counts[key] = []

        self.request_counts[key] = [
            timestamp
            for timestamp in self.request_counts[key]
            if timestamp > window_start
        ]

        # Check if limit exceeded
        limit = self.max_requests_per_window.get(method, 30)
        if len(self.request_counts[key]) >= limit:
            logger.warning(f"Rate limit exceeded for {client_ip} on {method}")
            return False

        # Add current request
        self.request_counts[key].append(current_time)
        return True

    def _cleanup_old_entries(self, cutoff_time: int):
        """Remove old entries to prevent memory leaks."""
        for key in list(self.request_counts.keys()):
            self.request_counts[key] = [
                timestamp
                for timestamp in self.request_counts[key]
                if timestamp > cutoff_time
            ]

            # Remove empty entries
            if not self.request_counts[key]:
                del self.request_counts[key]


class CSRFProtection:
    """Implements CSRF protection for state-changing operations."""

    def __init__(self):
        self.secret_key = os.environ.get(
            "CSRF_SECRET_KEY", "default-secret-change-in-production"
        )

    def generate_csrf_token(self, user_id: str) -> str:
        """Generate a CSRF token for a user."""
        timestamp = str(int(time.time()))
        payload = f"{user_id}:{timestamp}"

        signature = hmac.new(
            self.secret_key.encode(), payload.encode(), hashlib.sha256
        ).hexdigest()

        token = b64encode(f"{payload}:{signature}".encode()).decode()
        return token

    def validate_csrf_token(self, event: Dict[str, Any]) -> bool:
        """
        Validate CSRF token from request.

        Args:
            event: Lambda event

        Returns:
            bool: True if valid, False otherwise
        """
        # Skip CSRF validation for public auth endpoints
        path = event.get("path", "")
        if "/auth/" in path:
            return True

        # Get token from header
        headers = event.get("headers", {})
        csrf_token = headers.get("X-CSRF-Token") or headers.get("x-csrf-token")

        if not csrf_token:
            logger.warning("Missing CSRF token")
            return False

        try:
            # Decode token
            decoded = b64decode(csrf_token.encode()).decode()
            parts = decoded.split(":")

            if len(parts) != 3:
                logger.warning("Invalid CSRF token format")
                return False

            user_id, timestamp, signature = parts

            # Check token age (valid for 1 hour)
            token_age = int(time.time()) - int(timestamp)
            if token_age > 3600:
                logger.warning("Expired CSRF token")
                return False

            # Verify signature
            payload = f"{user_id}:{timestamp}"
            expected_signature = hmac.new(
                self.secret_key.encode(), payload.encode(), hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(signature, expected_signature):
                logger.warning("Invalid CSRF token signature")
                return False

            return True

        except Exception as e:
            logger.error(f"CSRF token validation error: {str(e)}")
            return False


# Custom exceptions
class SessionExpiredError(Exception):
    """Raised when a user session has expired."""

    pass


class InvalidSessionError(Exception):
    """Raised when a user session is invalid."""

    pass


class SecurityViolationError(Exception):
    """Raised when a security violation is detected."""

    pass
