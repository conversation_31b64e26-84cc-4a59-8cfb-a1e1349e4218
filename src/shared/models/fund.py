"""
Fund data model for DynamoDB operations.
Defines the structure and validation for Fund entities.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, cast
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    field_serializer,
    Field,
    field_validator,
    model_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel

# Import relationship models
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .relationships import (
        FundManager,
        InvestmentStrategy,
        FundHierarchy,
        FundBenchmark,
        FundDataSource,
    )
    from .market_data import (
        MarketDataSummary,
        PriceData,
        ValuationMetrics,
        TechnicalIndicators,
        RiskAnalytics,
        BenchmarkData,
    )


class FundType(str, Enum):
    """Fund type enumeration."""

    EQUITY = "equity"
    BOND = "bond"
    MIXED = "mixed"
    MONEY_MARKET = "money_market"
    ALTERNATIVE = "alternative"
    INDEX = "index"
    ETF = "etf"


class FundStatus(str, Enum):
    """Fund status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    LIQUIDATING = "liquidating"
    CLOSED = "closed"


class RiskLevel(str, Enum):
    """Risk level enumeration."""

    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


class Currency(str, Enum):
    """Currency enumeration."""

    USD = "USD"
    EUR = "EUR"
    GBP = "GBP"
    JPY = "JPY"
    CAD = "CAD"
    AUD = "AUD"
    CHF = "CHF"
    CNY = "CNY"
    HKD = "HKD"
    SGD = "SGD"
    KRW = "KRW"
    TWD = "TWD"
    INR = "INR"
    IDR = "IDR"
    MYR = "MYR"
    PHP = "PHP"
    THB = "THB"
    VND = "VND"
    ZAR = "ZAR"
    RUB = "RUB"
    TRY = "TRY"
    MXN = "MXN"
    PEN = "PEN"
    BRL = "BRL"
    CLP = "CLP"
    COP = "COP"
    KZT = "KZT"
    UAH = "UAH"
    PLN = "PLN"
    HUF = "HUF"
    CZK = "CZK"
    NOK = "NOK"
    SEK = "SEK"
    ILS = "ILS"
    AED = "AED"
    SAR = "SAR"
    QAR = "QAR"
    OMR = "OMR"
    KWD = "KWD"
    BHD = "BHD"
    JOD = "JOD"
    LYD = "LYD"
    DZD = "DZD"
    TND = "TND"


class PerformanceMetrics(BaseModel):
    """Enhanced performance metrics nested model."""

    # Return periods
    ytd_return: Optional[Decimal] = Field(
        None, description="Year-to-date return percentage"
    )
    one_day_return: Optional[Decimal] = Field(
        None, description="One day return percentage"
    )
    one_week_return: Optional[Decimal] = Field(
        None, description="One week return percentage"
    )
    one_month_return: Optional[Decimal] = Field(
        None, description="One month return percentage"
    )
    three_month_return: Optional[Decimal] = Field(
        None, description="Three month return percentage"
    )
    six_month_return: Optional[Decimal] = Field(
        None, description="Six month return percentage"
    )
    one_year_return: Optional[Decimal] = Field(
        None, description="One year return percentage"
    )
    three_year_return: Optional[Decimal] = Field(
        None, description="Three year return percentage"
    )
    five_year_return: Optional[Decimal] = Field(
        None, description="Five year return percentage"
    )
    inception_return: Optional[Decimal] = Field(
        None, description="Return since inception percentage"
    )

    # Risk-adjusted metrics
    volatility: Optional[Decimal] = Field(None, description="Volatility percentage")
    sharpe_ratio: Optional[Decimal] = Field(None, description="Sharpe ratio")
    sortino_ratio: Optional[Decimal] = Field(None, description="Sortino ratio")
    calmar_ratio: Optional[Decimal] = Field(None, description="Calmar ratio")
    information_ratio: Optional[Decimal] = Field(None, description="Information ratio")
    treynor_ratio: Optional[Decimal] = Field(None, description="Treynor ratio")

    # Risk metrics
    max_drawdown: Optional[Decimal] = Field(
        None, description="Maximum drawdown percentage"
    )
    downside_deviation: Optional[Decimal] = Field(
        None, description="Downside deviation percentage"
    )
    tracking_error: Optional[Decimal] = Field(
        None, description="Tracking error vs benchmark"
    )

    # Market risk
    alpha: Optional[Decimal] = Field(None, description="Alpha vs benchmark")
    beta: Optional[Decimal] = Field(None, description="Beta vs benchmark")
    correlation: Optional[Decimal] = Field(
        None, ge=-1, le=1, description="Correlation vs benchmark"
    )

    # Value at Risk
    var_1d_95: Optional[Decimal] = Field(None, description="1-day VaR at 95%")
    var_1d_99: Optional[Decimal] = Field(None, description="1-day VaR at 99%")

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer(
        "ytd_return",
        "one_day_return",
        "one_week_return",
        "one_month_return",
        "three_month_return",
        "six_month_return",
        "one_year_return",
        "three_year_return",
        "five_year_return",
        "inception_return",
        "volatility",
        "sharpe_ratio",
        "sortino_ratio",
        "calmar_ratio",
        "information_ratio",
        "treynor_ratio",
        "max_drawdown",
        "downside_deviation",
        "tracking_error",
        "alpha",
        "beta",
        "correlation",
        "var_1d_95",
        "var_1d_99",
        when_used="json",
    )
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("*", when_used="json")
    def serialize_datetime(self, value: Any) -> Any:
        """Serialize datetime objects to ISO format."""
        if isinstance(value, datetime):
            return value.isoformat()
        return value


class Holdings(BaseModel):
    """Enhanced fund holdings nested model."""

    top_holdings: Optional[List[Dict[str, Any]]] = Field(
        default_factory=list, description="Top holdings with detailed info"
    )
    sector_allocation: Optional[Dict[str, Decimal]] = Field(
        default_factory=dict, description="Sector allocation percentages"
    )
    geographic_allocation: Optional[Dict[str, Decimal]] = Field(
        default_factory=dict, description="Geographic allocation percentages"
    )

    # Asset allocation
    asset_allocation: Optional[Dict[str, Decimal]] = Field(
        default_factory=dict,
        description="Asset class allocation (stocks, bonds, cash, etc.)",
    )

    # Market cap allocation
    market_cap_allocation: Optional[Dict[str, Decimal]] = Field(
        default_factory=dict,
        description="Market cap allocation (large, mid, small cap)",
    )

    # Currency allocation
    currency_allocation: Optional[Dict[str, Decimal]] = Field(
        default_factory=dict, description="Currency exposure percentages"
    )

    # Holdings metadata
    total_holdings_count: Optional[int] = Field(
        None, ge=0, description="Total number of holdings"
    )
    holdings_concentration: Optional[Decimal] = Field(
        None, ge=0, le=100, description="Top 10 holdings concentration percentage"
    )
    last_updated: Optional[datetime] = Field(
        None, description="Holdings last updated timestamp"
    )

    model_config = ConfigDict(use_enum_values=True, validate_assignment=True)

    @field_serializer(
        "sector_allocation",
        "geographic_allocation",
        "asset_allocation",
        "market_cap_allocation",
        "currency_allocation",
        when_used="json",
    )
    def serialize_decimal_dict(
        self, value: Optional[Dict[str, Decimal]]
    ) -> Optional[Dict[str, str]]:
        """Serialize Decimal values in dictionaries to strings for JSON serialization."""
        if value is None:
            return None
        return {k: str(v) for k, v in value.items()}

    @field_serializer("holdings_concentration", when_used="json")
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("last_updated", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime to ISO format."""
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class Fund(PowertoolsBaseModel):
    """
    Fund data model for DynamoDB.

    DynamoDB Table: fundflow-{env}-funds
    Primary Key: fund_id (HASH)
    Global Secondary Indexes:
    - fund_type_index: fund_type (HASH), created_at (RANGE)
    - status_index: status (HASH), created_at (RANGE)
    """

    # Primary Key
    fund_id: str = Field(..., description="Unique fund identifier")

    # Basic Information
    name: str = Field(..., min_length=1, max_length=200, description="Fund name")
    fund_type: FundType = Field(..., description="Type of fund")
    status: FundStatus = Field(default=FundStatus.ACTIVE, description="Fund status")

    # Financial Details
    nav: Optional[Decimal] = Field(None, gt=0, description="Net Asset Value")
    currency: Currency = Field(default=Currency.USD, description="Base currency")
    inception_date: Optional[datetime] = Field(None, description="Fund inception date")
    total_assets: Optional[Decimal] = Field(
        None, ge=0, description="Total assets under management"
    )

    # Risk and Performance
    risk_level: Optional[RiskLevel] = Field(None, description="Risk level")
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics"
    )
    holdings: Optional[Holdings] = Field(None, description="Fund holdings information")

    # Management Information
    fund_manager: Optional[str] = Field(
        None, max_length=100, description="Fund manager name"
    )
    management_company: Optional[str] = Field(
        None, max_length=100, description="Management company"
    )
    expense_ratio: Optional[Decimal] = Field(
        None, ge=0, le=10, description="Annual expense ratio percentage"
    )
    minimum_investment: Optional[Decimal] = Field(
        None, ge=0, description="Minimum investment amount"
    )

    # Classification
    isin: Optional[str] = Field(
        None, pattern=r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$", description="ISIN code"
    )
    cusip: Optional[str] = Field(None, description="CUSIP identifier")
    bloomberg_ticker: Optional[str] = Field(None, description="Bloomberg ticker symbol")

    # Metadata
    description: Optional[str] = Field(
        None, max_length=1000, description="Fund description"
    )
    investment_objective: Optional[str] = Field(
        None, max_length=500, description="Investment objective"
    )
    benchmark: Optional[str] = Field(
        None, max_length=100, description="Benchmark index"
    )

    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp",
    )

    # Additional metadata
    tags: Optional[List[str]] = Field(
        default_factory=list, description="Fund tags for categorization"
    )
    custom_fields: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Custom fields for extensibility"
    )

    # Market data fields (optional, loaded separately for performance)
    market_data: Optional["MarketDataSummary"] = Field(
        None, description="Comprehensive market data summary"
    )
    current_price_data: Optional["PriceData"] = Field(
        None, description="Current price and trading data"
    )
    valuation_metrics: Optional["ValuationMetrics"] = Field(
        None, description="Fundamental valuation metrics"
    )
    technical_indicators: Optional["TechnicalIndicators"] = Field(
        None, description="Technical analysis indicators"
    )
    risk_analytics: Optional["RiskAnalytics"] = Field(
        None, description="Advanced risk analytics"
    )
    benchmark_data: Optional[List["BenchmarkData"]] = Field(
        default_factory=list, description="Benchmark comparison data"
    )

    # Relationship fields (optional, loaded separately for performance)
    fund_manager_details: Optional["FundManager"] = Field(
        None, description="Detailed fund manager information"
    )
    investment_strategy: Optional["InvestmentStrategy"] = Field(
        None, description="Investment strategy details"
    )
    fund_hierarchy: Optional["FundHierarchy"] = Field(
        None, description="Fund family and hierarchy information"
    )
    benchmarks: Optional[List["FundBenchmark"]] = Field(
        default_factory=list, description="Legacy benchmark comparison data"
    )
    data_sources: Optional[List["FundDataSource"]] = Field(
        default_factory=list, description="Data source information"
    )

    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, _v):
        """Always update the updated_at timestamp."""
        # _v parameter is required by pydantic but not used since we always set current time
        return datetime.now(timezone.utc)

    @field_validator("tags", mode="before")
    def ensure_tags_list(cls, v):
        """Ensure tags is always a list."""
        if v is None:
            return []
        return v

    @field_validator("custom_fields", mode="before")
    def ensure_custom_fields_dict(cls, v):
        """Ensure custom_fields is always a dict."""
        if v is None:
            return {}
        return v

    @field_validator("inception_date")
    def validate_inception_date(cls, v):
        """Validate inception date is not in the future."""
        if v and v > datetime.now(timezone.utc):
            raise ValueError("Inception date cannot be in the future")
        return v

    @field_validator("nav")
    def validate_nav(cls, v):
        """Validate NAV is a reasonable value."""
        if v is not None:
            if v <= 0:
                raise ValueError("NAV must be greater than 0")
            # Allow larger NAV values for extracted data (some funds have high NAV)
            if v > Decimal("100000"):
                # Log warning but don't reject - some funds legitimately have very high NAV
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Very high NAV detected: {v}")

        return v

    @field_validator("total_assets")
    def validate_total_assets(cls, v):
        """Validate total assets is reasonable."""
        if v is not None and v > Decimal("10000000000000"):  # 1 trillion
            raise ValueError("Total assets seems unreasonably high (> $10 trillion)")
        return v

    @field_validator("expense_ratio")
    def validate_expense_ratio(cls, v):
        """Validate expense ratio is reasonable for fund management."""
        if v is not None:
            if v < 0:
                raise ValueError("Expense ratio cannot be negative")

        return v

    @field_validator("minimum_investment")
    def validate_minimum_investment(cls, v):
        """Validate minimum investment amount."""
        if v is not None:
            if v < 0:
                raise ValueError("Minimum investment cannot be negative")

        return v

    @field_validator("fund_id")
    def validate_fund_id_format(cls, v):
        """Validate fund ID follows expected format."""
        if not v:
            raise ValueError("Fund ID is required")
        if len(v) < 3:
            raise ValueError("Fund ID must be at least 3 characters")
        if len(v) > 50:  # Maximum fund ID length
            raise ValueError("Fund ID must not exceed 50 characters")
        # Allow alphanumeric, hyphens, underscores, and dots for extracted fund IDs
        import re

        if not re.match(r"^[a-zA-Z0-9_.-]+$", v):
            raise ValueError(
                "Fund ID can only contain letters, numbers, hyphens, underscores, and dots"
            )
        return v

    @field_validator("cusip")
    def validate_cusip_format(cls, v):
        """Validate CUSIP format if provided."""
        if v is not None:
            import re

            if not re.match(r"^[A-Z0-9]{9}$", v):
                raise ValueError("CUSIP must be exactly 9 alphanumeric characters")
        return v

    @field_validator("bloomberg_ticker")
    def validate_bloomberg_ticker(cls, v):
        """Validate Bloomberg ticker format if provided."""
        if v is not None:
            if len(v) > 20:
                raise ValueError("Bloomberg ticker must not exceed 20 characters")
            import re

            if not re.match(r"^[A-Z0-9\.\-\s:]+$", v):
                raise ValueError("Bloomberg ticker contains invalid characters")
        return v

    @field_validator("performance_metrics")
    def validate_performance_metrics(cls, v):
        """Validate performance metrics consistency."""
        if v is not None:
            # Check that return percentages are reasonable (-100% to +1000%)
            return_fields = [
                "ytd_return",
                "one_year_return",
                "three_year_return",
                "five_year_return",
                "inception_return",
            ]

            for field in return_fields:
                value = getattr(v, field, None)
                if value is not None:
                    if value < Decimal("-100"):
                        raise ValueError(f"{field} cannot be less than -100%")
                    if value > Decimal("1000"):
                        raise ValueError(f"{field} seems unreasonably high (> 1000%)")

            # Validate volatility
            if v.volatility is not None:
                if v.volatility < 0:
                    raise ValueError("Volatility cannot be negative")

            # Validate max drawdown
            if v.max_drawdown is not None:
                if v.max_drawdown > 0:
                    raise ValueError("Max drawdown should be negative or zero")

        return v

    @field_validator("holdings")
    def validate_holdings(cls, v):
        """Validate holdings data consistency."""
        if v is not None:
            # Validate sector allocation percentages are reasonable
            # Note: Allow for incomplete allocations (partial sector coverage)

            # Validate geographic allocation percentages are reasonable
            # Note: Allow for incomplete allocations (partial geographic coverage)

            # Validate top holdings structure
            if v.top_holdings:
                if len(v.top_holdings) > 50:  # Reasonable limit
                    raise ValueError("Top holdings list cannot exceed 50 items")

                for holding in v.top_holdings:
                    if not isinstance(holding, dict):
                        raise ValueError("Each holding must be a dictionary")
                    if "percentage" in holding:
                        try:
                            pct = Decimal(str(holding["percentage"]))
                            if pct < 0 or pct > 100:
                                raise ValueError(
                                    "Holding percentage must be between 0 and 100"
                                )
                        except (ValueError, TypeError):
                            raise ValueError(
                                "Holding percentage must be a valid number"
                            )

        return v

    @model_validator(mode="after")
    def validate_status_transitions(self):
        """Validate fund status transitions and business rules."""
        # Additional status-specific validations could be added here
        # For now, just ensure closed/liquidating funds have appropriate data
        if self.status in [FundStatus.CLOSED, FundStatus.LIQUIDATING]:
            # Could add additional validation for closed funds
            pass
        return self

    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
    )

    @field_serializer(
        "nav", "total_assets", "expense_ratio", "minimum_investment", when_used="json"
    )
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None

    @field_serializer("created_at", "updated_at", "inception_date", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        if value is None:
            return None
        # Ensure datetime is timezone-aware before serialization
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class FundCreate(BaseModel):
    """Fund creation model (excludes auto-generated fields)."""

    name: str = Field(..., min_length=1, max_length=200)
    fund_type: FundType
    status: FundStatus = FundStatus.ACTIVE
    nav: Optional[Decimal] = None
    currency: Currency = Currency.USD
    inception_date: Optional[datetime] = None
    total_assets: Optional[Decimal] = None
    risk_level: Optional[RiskLevel] = None
    fund_manager: Optional[str] = None
    management_company: Optional[str] = None
    expense_ratio: Optional[Decimal] = None
    minimum_investment: Optional[Decimal] = None
    isin: Optional[str] = None
    cusip: Optional[str] = None
    bloomberg_ticker: Optional[str] = None
    description: Optional[str] = None
    investment_objective: Optional[str] = None
    benchmark: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class FundUpdate(BaseModel):
    """Fund update model (all fields optional)."""

    name: Optional[str] = Field(None, min_length=1, max_length=200)
    fund_type: Optional[FundType] = None
    status: Optional[FundStatus] = None
    nav: Optional[Decimal] = Field(None, gt=0)
    currency: Optional[Currency] = None
    inception_date: Optional[datetime] = None
    total_assets: Optional[Decimal] = Field(None, ge=0)
    risk_level: Optional[RiskLevel] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    holdings: Optional[Holdings] = None
    fund_manager: Optional[str] = Field(None, max_length=100)
    management_company: Optional[str] = Field(None, max_length=100)
    expense_ratio: Optional[Decimal] = Field(None, ge=0, le=10)
    minimum_investment: Optional[Decimal] = Field(None, ge=0)
    isin: Optional[str] = Field(None, pattern=r"^[A-Z]{2}[A-Z0-9]{9}[0-9]$")
    cusip: Optional[str] = None
    bloomberg_ticker: Optional[str] = None
    description: Optional[str] = Field(None, max_length=1000)
    investment_objective: Optional[str] = Field(None, max_length=500)
    benchmark: Optional[str] = Field(None, max_length=100)
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None


class FundResponse(Fund):
    """Fund response model for API responses."""

    pass


# DynamoDB specific utilities
class FundDynamoDBItem:
    """Utility class for DynamoDB item transformations."""

    @staticmethod
    def to_dynamodb_item(fund: Fund) -> Dict[str, Any]:
        """Convert Fund model to DynamoDB item format."""
        item = fund.model_dump()

        # Convert datetime objects to ISO strings
        for field in ["created_at", "updated_at", "inception_date"]:
            if item.get(field):
                item[field] = (
                    item[field].isoformat()
                    if isinstance(item[field], datetime)
                    else item[field]
                )

        # Convert Decimal objects to strings for DynamoDB
        def convert_decimals(obj):
            if isinstance(obj, dict):
                return {k: convert_decimals(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_decimals(v) for v in obj]
            elif isinstance(obj, Decimal):
                return str(obj)
            return obj

        # Since item is always a dict from fund.dict(), convert_decimals will return a dict
        return cast(Dict[str, Any], convert_decimals(item))

    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> Fund:
        """Convert DynamoDB item to Fund model."""
        # Convert string dates back to datetime objects
        for field in ["created_at", "updated_at", "inception_date"]:
            if item.get(field):
                if isinstance(item[field], str):
                    dt = datetime.fromisoformat(item[field])
                    # Ensure datetime is timezone-aware
                    if dt.tzinfo is None:
                        dt = dt.replace(tzinfo=timezone.utc)
                    item[field] = dt

        def convert_strings_to_decimals(obj, key=None, parent_key=None):
            if isinstance(obj, dict):
                # Handle special cases for nested dictionaries that should contain Decimal values
                if key in ["sector_allocation", "geographic_allocation"]:
                    return {
                        k: convert_strings_to_decimals(v, k, key)
                        for k, v in obj.items()
                    }
                else:
                    return {
                        k: convert_strings_to_decimals(v, k, parent_key)
                        for k, v in obj.items()
                    }
            elif isinstance(obj, list):
                return [convert_strings_to_decimals(v, None, key) for v in obj]
            elif isinstance(obj, str):
                # Convert based on field name
                if key in [
                    # Main Fund fields
                    "nav",
                    "total_assets",
                    "expense_ratio",
                    "minimum_investment",
                    # PerformanceMetrics fields
                    "ytd_return",
                    "one_year_return",
                    "three_year_return",
                    "five_year_return",
                    "inception_return",
                    "volatility",
                    "sharpe_ratio",
                    "max_drawdown",
                ] or parent_key in ["sector_allocation", "geographic_allocation"]:
                    try:
                        return Decimal(obj)
                    except:
                        return obj
            return obj

        converted_item = convert_strings_to_decimals(item)

        # Ensure converted_item is a dictionary with string keys
        if not isinstance(converted_item, dict):
            raise ValueError("Invalid DynamoDB item format: expected dictionary")

        # Cast to the expected type for type checker
        fund_data = cast(Dict[str, Any], converted_item)

        return Fund(**fund_data)
