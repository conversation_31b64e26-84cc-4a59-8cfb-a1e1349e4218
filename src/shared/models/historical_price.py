"""
Historical price data model for DynamoDB operations.
Defines the structure and validation for historical price data.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from decimal import Decimal
from enum import Enum

from pydantic import (
    BaseModel,
    field_serializer,
    Field,
    field_validator,
    ConfigDict,
)
from aws_lambda_powertools.utilities.parser import BaseModel as PowertoolsBaseModel


class DataSource(str, Enum):
    """Data source enumeration."""
    
    BLOOMBERG = "bloomberg"
    REUTERS = "reuters"
    MANUAL = "manual"
    YAHOO_FINANCE = "yahoo_finance"
    MORNINGSTAR = "morningstar"
    FUND_COMPANY = "fund_company"


class HistoricalPrice(PowertoolsBaseModel):
    """
    Historical price data model for DynamoDB.
    
    DynamoDB Table: fundflow-{env}-historical-prices
    Primary Key: fund_id (HASH), date (RANGE)
    Global Secondary Index: date-index: date (HASH), fund_id (RANGE)
    """
    
    # Primary Keys
    fund_id: str = Field(..., description="Fund identifier")
    date: str = Field(..., pattern=r"^\d{4}-\d{2}-\d{2}$", description="Date in YYYY-MM-DD format")
    
    # Price Data
    nav: Decimal = Field(..., gt=0, description="Net Asset Value")
    price: Optional[Decimal] = Field(None, gt=0, description="Trading price (for ETFs)")
    
    # Trading Data
    volume: Optional[Decimal] = Field(None, ge=0, description="Trading volume")
    total_return: Optional[Decimal] = Field(None, description="Daily total return percentage")
    
    # Benchmark Data
    benchmark_value: Optional[Decimal] = Field(None, gt=0, description="Benchmark value for comparison")
    benchmark_return: Optional[Decimal] = Field(None, description="Benchmark daily return percentage")
    
    # Additional Metrics
    dividend_amount: Optional[Decimal] = Field(None, ge=0, description="Dividend payment amount")
    split_ratio: Optional[Decimal] = Field(None, gt=0, description="Stock split ratio")
    
    # Metadata
    data_source: DataSource = Field(default=DataSource.MANUAL, description="Data source")
    currency: str = Field(default="USD", description="Currency code")
    
    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Creation timestamp"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp"
    )
    
    @field_validator("date")
    def validate_date_format(cls, v):
        """Validate date format and ensure it's not in the future."""
        try:
            date_obj = datetime.strptime(v, "%Y-%m-%d").date()
            today = datetime.now(timezone.utc).date()
            if date_obj > today:
                raise ValueError("Date cannot be in the future")
            return v
        except ValueError as e:
            if "time data" in str(e):
                raise ValueError("Date must be in YYYY-MM-DD format")
            raise e
    
    @field_validator("updated_at", mode="before")
    def set_updated_at(cls, _v):
        """Always update the updated_at timestamp."""
        return datetime.now(timezone.utc)
    
    @field_validator("nav")
    def validate_nav(cls, v):
        """Validate NAV is reasonable."""
        if v <= 0:
            raise ValueError("NAV must be greater than 0")
        if v > Decimal("100000"):
            import logging
            logger = logging.getLogger(__name__)
            logger.warning(f"Very high NAV detected: {v}")
        return v
    
    @field_validator("total_return", "benchmark_return")
    def validate_return_percentage(cls, v):
        """Validate return percentages are reasonable."""
        if v is not None:
            if v < Decimal("-50"):  # -50% daily return is extreme
                raise ValueError("Daily return cannot be less than -50%")
            if v > Decimal("50"):   # +50% daily return is extreme
                raise ValueError("Daily return cannot be more than +50%")
        return v
    
    model_config = ConfigDict(
        use_enum_values=True,
        validate_assignment=True,
    )
    
    @field_serializer(
        "nav", "price", "volume", "total_return", "benchmark_value", 
        "benchmark_return", "dividend_amount", "split_ratio",
        when_used="json"
    )
    def serialize_decimal(self, value: Optional[Decimal]) -> Optional[str]:
        """Serialize Decimal fields to string for JSON serialization."""
        return str(value) if value is not None else None
    
    @field_serializer("created_at", "updated_at", when_used="json")
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime fields to ISO format for JSON serialization."""
        if value is None:
            return None
        if value.tzinfo is None:
            value = value.replace(tzinfo=timezone.utc)
        return value.isoformat()


class HistoricalPriceCreate(BaseModel):
    """Historical price creation model."""
    
    fund_id: str
    date: str = Field(..., pattern=r"^\d{4}-\d{2}-\d{2}$")
    nav: Decimal = Field(..., gt=0)
    price: Optional[Decimal] = Field(None, gt=0)
    volume: Optional[Decimal] = Field(None, ge=0)
    total_return: Optional[Decimal] = None
    benchmark_value: Optional[Decimal] = Field(None, gt=0)
    benchmark_return: Optional[Decimal] = None
    dividend_amount: Optional[Decimal] = Field(None, ge=0)
    split_ratio: Optional[Decimal] = Field(None, gt=0)
    data_source: DataSource = DataSource.MANUAL
    currency: str = "USD"


class HistoricalPriceUpdate(BaseModel):
    """Historical price update model."""
    
    nav: Optional[Decimal] = Field(None, gt=0)
    price: Optional[Decimal] = Field(None, gt=0)
    volume: Optional[Decimal] = Field(None, ge=0)
    total_return: Optional[Decimal] = None
    benchmark_value: Optional[Decimal] = Field(None, gt=0)
    benchmark_return: Optional[Decimal] = None
    dividend_amount: Optional[Decimal] = Field(None, ge=0)
    split_ratio: Optional[Decimal] = Field(None, gt=0)
    data_source: Optional[DataSource] = None
    currency: Optional[str] = None


class HistoricalPriceResponse(HistoricalPrice):
    """Historical price response model for API responses."""
    pass


# DynamoDB specific utilities
class HistoricalPriceDynamoDBItem:
    """Utility class for DynamoDB item transformations."""
    
    @staticmethod
    def to_dynamodb_item(price: HistoricalPrice) -> Dict[str, Any]:
        """Convert HistoricalPrice model to DynamoDB item format."""
        item = price.model_dump()
        
        # Convert datetime objects to ISO strings
        for field in ["created_at", "updated_at"]:
            if item.get(field):
                item[field] = (
                    item[field].isoformat()
                    if isinstance(item[field], datetime)
                    else item[field]
                )
        
        # Convert Decimal objects to strings for DynamoDB
        def convert_decimals(obj):
            if isinstance(obj, dict):
                return {k: convert_decimals(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_decimals(v) for v in obj]
            elif isinstance(obj, Decimal):
                return str(obj)
            return obj
        
        return convert_decimals(item)
    
    @staticmethod
    def from_dynamodb_item(item: Dict[str, Any]) -> HistoricalPrice:
        """Convert DynamoDB item to HistoricalPrice model."""
        # Convert string dates back to datetime objects
        for field in ["created_at", "updated_at"]:
            if item.get(field):
                if isinstance(item[field], str):
                    dt = datetime.fromisoformat(item[field])
                    if dt.tzinfo is None:
                        dt = dt.replace(tzinfo=timezone.utc)
                    item[field] = dt
        
        # Convert string decimals back to Decimal objects
        decimal_fields = [
            "nav", "price", "volume", "total_return", "benchmark_value",
            "benchmark_return", "dividend_amount", "split_ratio"
        ]
        
        for field in decimal_fields:
            if item.get(field) and isinstance(item[field], str):
                try:
                    item[field] = Decimal(item[field])
                except:
                    pass
        
        return HistoricalPrice(**item)