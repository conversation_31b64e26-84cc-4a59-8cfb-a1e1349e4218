"""
Fund repository for DynamoDB operations.
"""

import os
from typing import Dict, List, Optional, Any
from decimal import Decimal
from aws_lambda_powertools import Logger

from .base import DynamoDBRepository
from ..models.fund import Fund, FundDynamoDBItem, FundType, FundStatus

# Initialize logger at module level
logger = Logger()


class FundRepository(DynamoDBRepository):
    """Repository for Fund entities in DynamoDB."""

    def __init__(self, region: str = "us-east-1"):
        """Initialize the Fund repository."""
        # Get table name from environment variable or construct it
        environment = os.getenv("ENVIRONMENT", "dev")
        table_name = f"fundflow-{environment}-funds"
        super().__init__(table_name, region)
        # Set instance logger to use the module logger
        self.logger = logger

    def _get_primary_key(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Get the primary key for a fund item."""
        return {"fund_id": item["fund_id"]}

    def _serialize_item(self, fund: Fund) -> Dict[str, Any]:
        """Serialize Fund object to DynamoDB format."""
        return FundDynamoDBItem.to_dynamodb_item(fund)

    def _deserialize_item(self, item: Dict[str, Any]) -> Fund:
        """Deserialize DynamoDB item to Fund object."""
        return FundDynamoDBItem.from_dynamodb_item(item)

    def get_by_fund_id(self, fund_id: str) -> Optional[Fund]:
        """Get a fund by its ID."""
        return self.get_by_id({"fund_id": fund_id})

    def get_by_type(
        self,
        fund_type: FundType,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
        newest_first: bool = True,
    ) -> Dict[str, Any]:
        """Get funds by type using the fund_type_index GSI."""
        return self.query_index(
            index_name="fund_type_index",
            key_condition="fund_type = :fund_type",
            expression_attribute_names={},
            expression_attribute_values={":fund_type": fund_type.value},
            limit=limit,
            last_evaluated_key=last_evaluated_key,
            scan_index_forward=not newest_first,  # False for descending (newest first)
        )

    def get_by_status(
        self,
        status: FundStatus,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
        newest_first: bool = True,
    ) -> Dict[str, Any]:
        """Get funds by status using the status_index GSI."""
        return self.query_index(
            index_name="status_index",
            key_condition="#status = :status",
            expression_attribute_names={"#status": "status"},
            expression_attribute_values={":status": status.value},
            limit=limit,
            last_evaluated_key=last_evaluated_key,
            scan_index_forward=not newest_first,
        )

    def get_active_funds(
        self, limit: Optional[int] = None, last_evaluated_key: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Get all active funds."""
        return self.get_by_status(FundStatus.ACTIVE, limit, last_evaluated_key)

    def search_by_name(
        self,
        search_term: str,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Search funds by name using scan with filter."""
        try:
            scan_kwargs = {
                "FilterExpression": "contains(#name, :search_term)",
                "ExpressionAttributeNames": {"#name": "name"},
                "ExpressionAttributeValues": {":search_term": search_term.lower()},
            }

            if limit:
                scan_kwargs["Limit"] = limit
            if last_evaluated_key:
                scan_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.scan(**scan_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "items": items,
                "count": len(items),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            return result

        except Exception as e:
            self.logger.error(
                f"Error searching funds by name",
                extra={"error": str(e), "search_term": search_term},
            )
            raise

    def get_funds_by_nav_range(
        self,
        min_nav: Optional[Decimal] = None,
        max_nav: Optional[Decimal] = None,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Get funds within a NAV range using scan with filter."""
        try:
            filter_conditions = []
            expression_attribute_values = {}

            if min_nav is not None:
                filter_conditions.append("nav >= :min_nav")
                expression_attribute_values[":min_nav"] = str(min_nav)

            if max_nav is not None:
                filter_conditions.append("nav <= :max_nav")
                expression_attribute_values[":max_nav"] = str(max_nav)

            if not filter_conditions:
                # If no range specified, return all funds
                return self.list_all(limit, last_evaluated_key)

            scan_kwargs = {
                "FilterExpression": " AND ".join(filter_conditions),
                "ExpressionAttributeValues": expression_attribute_values,
            }

            if limit:
                scan_kwargs["Limit"] = limit
            if last_evaluated_key:
                scan_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.scan(**scan_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "items": items,
                "count": len(items),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            return result

        except Exception as e:
            self.logger.error(
                f"Error getting funds by NAV range",
                extra={
                    "error": str(e),
                    "min_nav": str(min_nav),
                    "max_nav": str(max_nav),
                },
            )
            raise

    def update_nav(self, fund_id: str, new_nav: Decimal) -> bool:
        """Update a fund's NAV value."""
        try:
            self.table.update_item(
                Key={"fund_id": fund_id},
                UpdateExpression="SET nav = :nav, updated_at = :updated_at",
                ExpressionAttributeValues={
                    ":nav": str(new_nav),
                    ":updated_at": self._get_current_timestamp(),
                },
                ConditionExpression="attribute_exists(fund_id)",
            )

            self.logger.info(
                f"Updated NAV for fund {fund_id}",
                extra={"fund_id": fund_id, "new_nav": str(new_nav)},
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Error updating fund NAV",
                extra={"error": str(e), "fund_id": fund_id, "new_nav": str(new_nav)},
            )
            raise

    def update_status(self, fund_id: str, new_status: FundStatus) -> bool:
        """Update a fund's status."""
        try:
            self.table.update_item(
                Key={"fund_id": fund_id},
                UpdateExpression="SET #status = :status, updated_at = :updated_at",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={
                    ":status": new_status.value,
                    ":updated_at": self._get_current_timestamp(),
                },
                ConditionExpression="attribute_exists(fund_id)",
            )

            self.logger.info(
                f"Updated status for fund {fund_id}",
                extra={"fund_id": fund_id, "new_status": new_status.value},
            )
            return True

        except Exception as e:
            self.logger.error(
                f"Error updating fund status",
                extra={
                    "error": str(e),
                    "fund_id": fund_id,
                    "new_status": new_status.value,
                },
            )
            raise

    def get_funds_by_manager(
        self,
        fund_manager: str,
        limit: Optional[int] = None,
        last_evaluated_key: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Get funds by fund manager using scan with filter."""
        try:
            scan_kwargs = {
                "FilterExpression": "fund_manager = :manager",
                "ExpressionAttributeValues": {":manager": fund_manager},
            }

            if limit:
                scan_kwargs["Limit"] = limit
            if last_evaluated_key:
                scan_kwargs["ExclusiveStartKey"] = last_evaluated_key

            response = self.table.scan(**scan_kwargs)

            items = [self._deserialize_item(item) for item in response.get("Items", [])]

            result = {
                "items": items,
                "count": len(items),
                "scanned_count": response.get("ScannedCount", 0),
            }

            if "LastEvaluatedKey" in response:
                result["last_evaluated_key"] = response["LastEvaluatedKey"]

            return result

        except Exception as e:
            self.logger.error(
                f"Error getting funds by manager",
                extra={"error": str(e), "fund_manager": fund_manager},
            )
            raise

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        from datetime import datetime, timezone

        return datetime.now(timezone.utc).isoformat()

    def get_fund(self, fund_id: str) -> Optional[Fund]:
        """Get a fund by its ID."""
        return self.get_by_fund_id(fund_id)

    def list_funds(
        self,
        limit: Optional[int] = None,
        last_key: Optional[str] = None,
        status: Optional[str] = None,
        fund_type: Optional[str] = None,
        search: Optional[str] = None,
    ) -> Dict[str, Any]:
        """List funds with optional filtering."""
        try:
            # If search is provided, use search functionality
            if search:
                result = self.search_by_name(search, limit, last_key)
                return {
                    "funds": result["items"],
                    "count": result["count"],
                    "has_more": "last_evaluated_key" in result,
                    "last_key": result.get("last_evaluated_key"),
                }

            # If status filter is provided
            if status:
                try:
                    status_enum = FundStatus(status)
                    result = self.get_by_status(status_enum, limit, last_key)
                    return {
                        "funds": result["items"],
                        "count": result["count"],
                        "has_more": "last_evaluated_key" in result,
                        "last_key": result.get("last_evaluated_key"),
                    }
                except ValueError:
                    raise ValueError(f"Invalid status: {status}")

            # If fund type filter is provided
            if fund_type:
                try:
                    type_enum = FundType(fund_type)
                    result = self.get_by_type(type_enum, limit, last_key)
                    return {
                        "funds": result["items"],
                        "count": result["count"],
                        "has_more": "last_evaluated_key" in result,
                        "last_key": result.get("last_evaluated_key"),
                    }
                except ValueError:
                    raise ValueError(f"Invalid fund type: {fund_type}")

            # Default: list all funds
            result = self.list_all(limit, last_key)
            return {
                "funds": result["items"],
                "count": result["count"],
                "has_more": "last_evaluated_key" in result,
                "last_key": result.get("last_evaluated_key"),
            }

        except Exception as e:
            self.logger.error(f"Error listing funds", extra={"error": str(e)})
            raise

    def create_fund(self, fund_data, user_id: str) -> Fund:
        """Create a new fund."""
        from datetime import datetime, timezone

        fund = Fund(
            fund_id=self._generate_id(),
            **fund_data.dict(),
            created_by=user_id,
            updated_by=user_id,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
        )

        self.logger.info(f"Creating fund: {fund.fund_id}")

        try:
            item = self._serialize_item(fund)

            self.table.put_item(
                Item=item, ConditionExpression="attribute_not_exists(fund_id)"
            )

            self.logger.info(f"Fund created successfully: {fund.fund_id}")
            return fund

        except Exception as e:
            if "ConditionalCheckFailedException" in str(e):
                raise ValueError(f"Fund with ID {fund.fund_id} already exists")
            raise

    def update_fund(self, fund_id: str, update_data, user_id: str) -> Fund:
        """Update an existing fund."""
        from datetime import datetime, timezone

        self.logger.info(f"Updating fund: {fund_id}")

        try:
            # Get current fund
            current_fund = self.get_fund(fund_id)
            if not current_fund:
                raise ValueError(f"Fund with ID {fund_id} not found")

            # Apply updates
            update_dict = update_data.dict(exclude_unset=True)
            update_dict["updated_by"] = user_id
            update_dict["updated_at"] = datetime.now(timezone.utc)

            # Build update expression
            update_expression_parts = []
            expression_attribute_names = {}
            expression_attribute_values = {}

            for key, value in update_dict.items():
                attr_name = f"#{key}"
                attr_value = f":{key}"
                update_expression_parts.append(f"{attr_name} = {attr_value}")
                expression_attribute_names[attr_name] = key
                expression_attribute_values[attr_value] = self._serialize_value(value)

            update_expression = "SET " + ", ".join(update_expression_parts)

            # Perform update
            response = self.table.update_item(
                Key={"fund_id": fund_id},
                UpdateExpression=update_expression,
                ExpressionAttributeNames=expression_attribute_names,
                ExpressionAttributeValues=expression_attribute_values,
                ReturnValues="ALL_NEW",
                ConditionExpression="attribute_exists(fund_id)",
            )

            # Convert response to Fund object
            updated_fund = self._deserialize_item(response["Attributes"])

            self.logger.info(f"Fund updated successfully: {fund_id}")
            return updated_fund

        except Exception as e:
            if "ConditionalCheckFailedException" in str(e):
                raise ValueError(f"Fund with ID {fund_id} not found")
            raise

    def soft_delete_fund(self, fund_id: str, user_id: str) -> bool:
        """Soft delete a fund by setting status to inactive."""
        from datetime import datetime, timezone

        self.logger.info(f"Soft deleting fund: {fund_id}")

        try:
            self.table.update_item(
                Key={"fund_id": fund_id},
                UpdateExpression="SET #status = :status, #updated_by = :updated_by, #updated_at = :updated_at",
                ExpressionAttributeNames={
                    "#status": "status",
                    "#updated_by": "updated_by",
                    "#updated_at": "updated_at",
                },
                ExpressionAttributeValues={
                    ":status": "inactive",
                    ":updated_by": user_id,
                    ":updated_at": self._serialize_value(datetime.now(timezone.utc)),
                },
                ConditionExpression="attribute_exists(fund_id)",
            )

            self.logger.info(f"Fund soft deleted successfully: {fund_id}")
            return True

        except Exception as e:
            if "ConditionalCheckFailedException" in str(e):
                raise ValueError(f"Fund with ID {fund_id} not found")
            raise
