# Mock Mode Consolidation

## Overview

The mock flags have been consolidated into a single environment variable for easier development setup. Instead of setting multiple flags individually, you can now use one flag to enable all mock functionality.

## New Consolidated Flag

### `NEXT_PUBLIC_ENABLE_MOCK_MODE`

- **Purpose**: Enables complete mock mode (login, API fallback, and data)
- **Values**: `true` or `false`
- **Default**: `false`
- **When enabled**: 
  - Mock authentication (bypasses Cognito)
  - Mock data fallback for all API calls
  - No AWS dependencies required

## Quick Setup

### Enable Complete Mock Mode

Add this single line to your `.env.local`:

```env
NEXT_PUBLIC_ENABLE_MOCK_MODE=true
```

This replaces the need to set:
```env
# OLD WAY (still supported for backward compatibility)
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true
NEXT_PUBLIC_USE_AWS_API=false
```

### Using the Toggle Script

The toggle script has been updated to use the new flag:

```bash
cd frontend

# Enable mock mode (sets NEXT_PUBLIC_ENABLE_MOCK_MODE=true)
npm run toggle-mock mock

# Enable external mode (sets NEXT_PUBLIC_ENABLE_MOCK_MODE=false)
npm run toggle-mock external

# Check current status
npm run toggle-mock status
```

## Backward Compatibility

The old flags are still supported for backward compatibility:

- `NEXT_PUBLIC_ENABLE_MOCK_FALLBACK`
- `NEXT_PUBLIC_ENABLE_MOCK_LOGIN`
- `NEXT_PUBLIC_USE_AWS_API`

If `NEXT_PUBLIC_ENABLE_MOCK_MODE` is set, it takes precedence over the legacy flags.

## Configuration Modes

### Complete Mock Mode (Recommended for Development)
```env
NEXT_PUBLIC_ENABLE_MOCK_MODE=true
```
- Mock authentication available
- All API calls return mock data
- No AWS dependencies required

### Production Mode
```env
NEXT_PUBLIC_ENABLE_MOCK_MODE=false
# or simply omit the variable
```
- Real Cognito authentication only
- Real API calls to AWS services
- Production behavior

## Migration Guide

### If you're currently using:
```env
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true
NEXT_PUBLIC_USE_AWS_API=false
```

### Simply replace with:
```env
NEXT_PUBLIC_ENABLE_MOCK_MODE=true
```

You can remove the old flags, but they can remain for backward compatibility.

## Benefits

1. **Simplified Setup**: One flag instead of three
2. **Less Configuration Errors**: No need to remember multiple flag combinations
3. **Clearer Intent**: The flag name clearly indicates complete mock mode
4. **Backward Compatible**: Existing configurations continue to work
5. **Future-Proof**: Easier to add new mock features under the single flag

## Implementation Details

The consolidation is implemented with backward compatibility:

- If `NEXT_PUBLIC_ENABLE_MOCK_MODE=true`, all mock features are enabled
- If `NEXT_PUBLIC_ENABLE_MOCK_MODE=false` or unset, the system falls back to checking legacy flags
- Legacy flags continue to work as before for existing setups

This ensures a smooth transition without breaking existing development environments.
