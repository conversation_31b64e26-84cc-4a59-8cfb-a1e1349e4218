# Mock Login Setup Guide

This guide explains how to set up and use the mock login feature for complete local development without requiring AWS Cognito or backend services.

## Overview

The mock login feature allows you to:

- Test the UI completely locally without communicating with the backend
- Bypass AWS Cognito authentication during development
- Use mock data for all API responses
- Develop and test frontend features without AWS dependencies

## Environment Variables

Two environment variables control the mock functionality:

### `NEXT_PUBLIC_ENABLE_MOCK_FALLBACK`

- **Purpose**: Enables mock data fallback when AWS API calls fail
- **Values**: `true` or `false`
- **Default**: `false`

### `NEXT_PUBLIC_ENABLE_MOCK_LOGIN` (NEW)

- **Purpose**: Enables mock authentication (bypasses Cognito)
- **Values**: `true` or `false`
- **Default**: `false`
- **Requirement**: Should be used with `ENABLE_MOCK_FALLBACK=true`

## Setup Instructions

### 1. Enable Mock Mode

**Option A: Use the Toggle Script (Recommended)**

```bash
cd frontend
npm run toggle-mock mock
```

**Option B: Manual Configuration**
Edit your `.env.local` file:

```env
# Comment out external URLs
# NEXT_PUBLIC_APP_URL=https://your-external-url.com
# NEXTAUTH_URL=https://your-external-url.com

# Use localhost URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000

# Enable mock features
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true

# Optional: Disable AWS API to force mock mode
NEXT_PUBLIC_USE_AWS_API=false
```

### 2. Restart Development Server

```bash
cd frontend
npm run dev
```

### 3. Access Mock Login

1. Navigate to your application (e.g., `http://localhost:3000`)
2. Click "Sign In"
3. You'll see two options:
   - **Sign in with Cognito** (real authentication)
   - **Mock Login (Development Only)** (mock authentication)

### 4. Use Mock Login

1. Click "Mock Login (Development Only)"
2. Enter any email address (e.g., `<EMAIL>`)
3. Enter any password (it will be accepted)
4. You'll be signed in with a mock session

## How It Works

### Authentication Flow

1. **Mock Provider**: A new NextAuth.js credentials provider is added when `ENABLE_MOCK_LOGIN=true`
2. **URL Override**: When mock login is enabled, NextAuth automatically uses `localhost:3000` instead of external URLs
3. **Mock Session**: Creates a fake session with mock access tokens
4. **API Integration**: The API client detects mock sessions and uses mock data directly

### API Behavior

- **Mock Session Detected**: API calls return mock data immediately (no network requests)
- **Real Session**: API calls go to AWS (with fallback to mock data if `ENABLE_MOCK_FALLBACK=true`)
- **No Session**: API calls fail with authentication errors

### Mock Data

- **Funds**: 50 generated funds with realistic data
- **Performance**: Random but realistic performance metrics
- **Historical Data**: Generated time series data
- **Market Data**: Mock valuation and technical indicators

## Quick Mode Switching

Use the toggle script to easily switch between modes:

```bash
# Enable mock mode (localhost + mock features)
npm run toggle-mock mock

# Enable external mode (pinggy.link + real auth)
npm run toggle-mock external

# Check current status
npm run toggle-mock status
```

## Testing Scenarios

### Complete Local Testing

```env
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true
NEXT_PUBLIC_USE_AWS_API=false
```

- No AWS dependencies
- All data is mocked
- Perfect for UI development

### Hybrid Testing

```env
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=true
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true
NEXT_PUBLIC_USE_AWS_API=true
```

- Mock login available
- Real API calls with mock fallback
- Good for testing error scenarios

### Production-like Testing

```env
NEXT_PUBLIC_ENABLE_MOCK_FALLBACK=false
NEXT_PUBLIC_ENABLE_MOCK_LOGIN=false
NEXT_PUBLIC_USE_AWS_API=true
```

- Only real Cognito authentication
- Only real API calls
- Production behavior

## Security Notes

⚠️ **IMPORTANT SECURITY WARNINGS**:

1. **Never enable in production**: Mock login bypasses all security
2. **Development only**: Only use in local development environments
3. **Environment check**: The system prevents mock login in production builds
4. **Clear documentation**: Always document when mock mode is enabled

## Troubleshooting

### Mock Login Button Not Showing

- Check that `NEXT_PUBLIC_ENABLE_MOCK_LOGIN=true` in `.env.local`
- Restart the development server
- Clear browser cache

### Mock Login Not Working

- Ensure both `ENABLE_MOCK_LOGIN` and `ENABLE_MOCK_FALLBACK` are `true`
- Check browser console for error messages
- Verify environment variables are loaded correctly

### API Still Calling Real Backend

- Check that the session provider is `mock-login` in browser dev tools
- Verify API client is detecting mock sessions correctly
- Enable debug logging in browser console

### Redirecting to External URL Instead of Localhost

- Use the toggle script: `npm run toggle-mock mock`
- Manually check `.env.local` has `NEXTAUTH_URL=http://localhost:3000` (not commented out)
- Ensure external URLs are commented out: `# NEXTAUTH_URL=https://external-url.com`
- Restart development server after changing environment variables
- Clear browser cache and cookies

## Development Workflow

### Recommended Setup for UI Development

1. Set `ENABLE_MOCK_LOGIN=true` and `ENABLE_MOCK_FALLBACK=true`
2. Use mock login for quick authentication
3. Develop UI components with mock data
4. Test with real authentication before deployment

### Testing Authentication Flows

1. Test both mock and real authentication
2. Verify session handling works correctly
3. Test token expiration scenarios
4. Validate error handling

## Files Modified

- `frontend/src/lib/auth.ts`: Added mock credentials provider
- `frontend/src/lib/api.ts`: Added mock session detection
- `frontend/src/app/test-auth/page.tsx`: Added mock login button
- `frontend/.env.local`: Configured for localhost URLs in mock mode
- `frontend/scripts/toggle-mock-mode.js`: Script to switch between modes
- `frontend/package.json`: Added toggle-mock script
- `frontend/env.example`: Added configuration documentation

## Next Steps

After setting up mock login, you can:

1. Develop UI components without backend dependencies
2. Test authentication flows quickly
3. Create comprehensive frontend tests
4. Demonstrate features without AWS setup
