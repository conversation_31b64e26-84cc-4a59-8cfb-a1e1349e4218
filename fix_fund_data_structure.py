#!/usr/bin/env python3
"""
Fix Fund Data Structure Issues

This script fixes the data structure issues found in DynamoDB:
1. Add missing 'id' field (using fund_id)
2. Create proper 'analytics' field structure from existing data
3. Ensure data matches what the frontend expects
"""

import boto3
from decimal import Decimal
import json
from datetime import datetime

def convert_to_decimal(obj):
    """Convert numbers to Decimal for DynamoDB"""
    if isinstance(obj, dict):
        return {k: convert_to_decimal(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_decimal(item) for item in obj]
    elif isinstance(obj, (int, float)):
        return Decimal(str(obj))
    elif isinstance(obj, str):
        try:
            # Try to convert numeric strings to Decimal
            return Decimal(obj)
        except:
            return obj
    else:
        return obj

def create_analytics_structure(fund):
    """Create the analytics structure that the frontend expects"""
    analytics = {}
    
    # KPIs from performance metrics
    if 'performance_metrics' in fund and fund['performance_metrics']:
        analytics['kpis'] = {
            'totalReturn': fund['performance_metrics'].get('ytd_return', '0'),
            'annualizedReturn': fund['performance_metrics'].get('one_year_return', '0'),
            'volatility': fund['performance_metrics'].get('volatility', '0'),
            'sharpeRatio': fund['performance_metrics'].get('sharpe_ratio', '0'),
            'maxDrawdown': fund['performance_metrics'].get('max_drawdown', '0'),
            'threeYearReturn': fund['performance_metrics'].get('three_year_return', '0'),
            'fiveYearReturn': fund['performance_metrics'].get('five_year_return', '0'),
        }
    else:
        # Create default KPIs if no performance metrics
        analytics['kpis'] = {
            'totalReturn': '0',
            'annualizedReturn': '0',
            'volatility': '0',
            'sharpeRatio': '0',
            'maxDrawdown': '0',
            'threeYearReturn': '0',
            'fiveYearReturn': '0',
        }
    
    # Risk metrics from risk_analytics
    if 'risk_analytics' in fund and fund['risk_analytics']:
        analytics['riskMetrics'] = {
            'beta': fund['risk_analytics'].get('beta_vs_benchmark', '0'),
            'alpha': '0',  # Not in current data
            'correlation': fund['risk_analytics'].get('correlation_vs_benchmark', '0'),
            'trackingError': fund['risk_analytics'].get('tracking_error', '0'),
            'informationRatio': fund['risk_analytics'].get('information_ratio', '0'),
            'sortinoRatio': fund['risk_analytics'].get('sortino_ratio', '0'),
            'var95': fund['risk_analytics'].get('var_1d_95', '0'),
            'var99': fund['risk_analytics'].get('var_1d_99', '0'),
            'cvar95': fund['risk_analytics'].get('cvar_1d_95', '0'),
            'cvar99': fund['risk_analytics'].get('cvar_1d_99', '0'),
        }
    else:
        # Create default risk metrics
        analytics['riskMetrics'] = {
            'beta': '0',
            'alpha': '0',
            'correlation': '0',
            'trackingError': '0',
            'informationRatio': '0',
            'sortinoRatio': '0',
            'var95': '0',
            'var99': '0',
            'cvar95': '0',
            'cvar99': '0',
        }
    
    # Allocations from holdings
    if 'holdings' in fund:
        if 'sector_allocation' in fund['holdings']:
            analytics['sectorAllocation'] = fund['holdings']['sector_allocation']
        
        if 'geographic_allocation' in fund['holdings']:
            analytics['geographicAllocation'] = fund['holdings']['geographic_allocation']
            
        if 'asset_allocation' in fund['holdings']:
            analytics['assetAllocation'] = fund['holdings']['asset_allocation']
            
        if 'top_holdings' in fund['holdings']:
            analytics['topHoldings'] = fund['holdings']['top_holdings']
    
    return analytics

def main():
    # Connect to DynamoDB
    dynamodb = boto3.resource('dynamodb', region_name='ap-northeast-1')
    funds_table = dynamodb.Table('fundflow-dev-funds')
    
    print("Fixing fund data structure issues...")
    
    # Get all funds
    response = funds_table.scan()
    funds = response['Items']
    
    print(f"Found {len(funds)} funds to fix")
    
    for fund in funds:
        fund_name = fund.get('name', 'Unknown')
        fund_id = fund.get('fund_id', 'Unknown')
        
        print(f"\nProcessing: {fund_name}")
        
        # Create the key for the update
        key = {}
        if 'fund_id' in fund:
            key['fund_id'] = fund['fund_id']
        else:
            # If no fund_id, we need to identify the fund by name
            key['name'] = fund['name']
        
        # Prepare update expression and values
        update_expression = "SET "
        expression_attribute_values = {}
        expression_attribute_names = {}
        
        updates = []
        
        # 1. Add 'id' field if missing
        if 'id' not in fund and 'fund_id' in fund:
            updates.append("#id = :id")
            expression_attribute_names['#id'] = 'id'
            expression_attribute_values[':id'] = fund['fund_id']
            print(f"  ✓ Adding id field: {fund['fund_id']}")
        
        # 2. Create analytics structure
        analytics = create_analytics_structure(fund)
        if analytics:
            updates.append("analytics = :analytics")
            expression_attribute_values[':analytics'] = convert_to_decimal(analytics)
            print(f"  ✓ Creating analytics structure with {len(analytics)} sections")
        
        # 3. Ensure market_data is properly structured
        if 'market_data' in fund:
            # The market_data is already properly structured, just ensure it's in the right format
            updates.append("market_data = :market_data")
            expression_attribute_values[':market_data'] = convert_to_decimal(fund['market_data'])
            print(f"  ✓ Updating market_data structure")
        
        # 4. Ensure holdings are properly structured
        if 'holdings' in fund:
            # Convert holdings to the format expected by frontend
            holdings_data = fund['holdings']
            
            # Convert top_holdings to expected format
            if 'top_holdings' in holdings_data:
                top_holdings = []
                for holding in holdings_data['top_holdings']:
                    top_holdings.append({
                        'name': holding.get('name', ''),
                        'symbol': holding.get('symbol', ''),
                        'percentage': holding.get('percentage', '0'),
                        'marketValue': holding.get('market_value', '0'),
                        'sector': holding.get('sector', ''),
                        'shares': holding.get('shares', '0')
                    })
                holdings_data['topHoldings'] = top_holdings
            
            updates.append("holdings = :holdings")
            expression_attribute_values[':holdings'] = convert_to_decimal(holdings_data)
            print(f"  ✓ Updating holdings structure")
        
        # Only update if we have changes
        if updates:
            update_expression += ", ".join(updates)
            
            try:
                # Update the item
                funds_table.update_item(
                    Key=key,
                    UpdateExpression=update_expression,
                    ExpressionAttributeValues=expression_attribute_values,
                    ExpressionAttributeNames=expression_attribute_names if expression_attribute_names else None
                )
                print(f"  ✅ Successfully updated {fund_name}")
                
            except Exception as e:
                print(f"  ❌ Error updating {fund_name}: {str(e)}")
                # Try with just the fund_id as key
                if 'fund_id' in fund:
                    try:
                        funds_table.update_item(
                            Key={'fund_id': fund['fund_id']},
                            UpdateExpression=update_expression,
                            ExpressionAttributeValues=expression_attribute_values,
                            ExpressionAttributeNames=expression_attribute_names if expression_attribute_names else None
                        )
                        print(f"  ✅ Successfully updated {fund_name} (using fund_id key)")
                    except Exception as e2:
                        print(f"  ❌ Final error updating {fund_name}: {str(e2)}")
        else:
            print(f"  ⚠️  No updates needed for {fund_name}")
    
    print("\n" + "="*50)
    print("Data structure fix complete!")
    print("="*50)
    
    # Verify the fixes
    print("\nVerifying fixes...")
    response = funds_table.scan()
    funds = response['Items']
    
    for fund in funds:
        fund_name = fund.get('name', 'Unknown')
        fund_id = fund.get('id', fund.get('fund_id', 'No ID'))
        
        has_id = 'id' in fund
        has_analytics = 'analytics' in fund
        has_market_data = 'market_data' in fund
        has_holdings = 'holdings' in fund
        
        print(f"{fund_name} ({fund_id}): ID={has_id}, Analytics={has_analytics}, Market={has_market_data}, Holdings={has_holdings}")

if __name__ == "__main__":
    main()